import memberCardPreview from '@/pages/nestle/commonDecorate/modules/MemberCard/preview';
import memberCardDecorate from '@/pages/nestle/commonDecorate/modules/MemberCard';

import newRights from '@/pages/nestle/commonDecorate/modules/NewRights';
import newRightsPreview from '@/pages/nestle/commonDecorate/modules/NewRights/preview';

import oldRights from '@/pages/nestle/commonDecorate/modules/OldRights';
import oldRightsPreview from '@/pages/nestle/commonDecorate/modules/OldRights/preview';

import fanPrivilege from '@/pages/nestle/commonDecorate/modules/FanPrivilege';
import fanPrivilegePreview from '@/pages/nestle/commonDecorate/modules/FanPrivilege/preview';

import memberNotes from '@/pages/nestle/commonDecorate/modules/MemberNotes';
import memberNotesPreview from '@/pages/nestle/commonDecorate/modules/MemberNotes/preview';

import consume from '@/pages/nestle/commonDecorate/modules/Consume';
import consumePreview from '@/pages/nestle/commonDecorate/modules/Consume/preview';
export const initialState = () => {
  return {
    selectedModule: 'memberCard',
    previews: {
      memberCard: {
        previewComponent: memberCardPreview,
        decorateComponent: memberCardDecorate,
        name: '会员卡模块',
      },
      consume: {
        previewComponent: consumePreview,
        decorateComponent: consume,
        name: '消费特权',
      },
      monthCoupon: {
        previewComponent: newRightsPreview,
        decorateComponent: newRights,
        name: '新客权益',
      },
      oldRights: {
        previewComponent: oldRightsPreview,
        decorateComponent: oldRights,
        name: '老客权益',
      },
      fanPrivilege: {
        previewComponent: fanPrivilegePreview,
        decorateComponent: fanPrivilege,
        name: '巢粉特权',
      },
      memberNotes: {
        previewComponent: memberNotesPreview,
        decorateComponent: memberNotes,
        name: '会员须知',
      },
    },
    modules: {
      memberCard: {
        bg: '//img10.360buyimg.com/imgzone/jfs/t1/276981/17/26536/96409/6808da55F68ea176b/c490cded7d5ab1e5.jpg',
        processUnderColor: '#fff',
        processColor: '#eb9900',
        upgradeBtnBg:
          '//img10.360buyimg.com/imgzone/jfs/t1/284304/13/17298/7031/67f883b7F547568ae/ae5a54ace14980fd.png',
        upgradeBtnLink: 'https://mall.jd.com/index-1000003112.html',
        levels: ['萌新妈妈', '营养妈妈', '全能妈妈', '金牌妈妈', '钻石妈妈'],
        upgradeTitleImg:
          '//img10.360buyimg.com/imgzone/jfs/t1/270928/11/27110/16437/6808d569F66073b13/998962268cf72998.png',
        upgradeBg: '//img10.360buyimg.com/imgzone/jfs/t1/277007/30/26464/4429/68089775F72f2c2d8/e7c565d178029a9e.png',
        tipImg: '',
        upgradeImgList: [
          {
            '3': '//img10.360buyimg.com/imgzone/jfs/t1/280407/9/24450/20159/6808a0b1Fb6931918/f99e145b009152b2.png',
            prizeList: [],
          },
          {
            '4': '//img10.360buyimg.com/imgzone/jfs/t1/273565/19/25255/19622/6808a0b0F172083aa/30f10e5f5f7c4f39.png',
            prizeList: [],
          },
          {
            '5': '//img10.360buyimg.com/imgzone/jfs/t1/272254/15/24029/21944/6808a0b0Feeb0a6ea/52ed2c36feff487f.png',
            prizeList: [],
          },
        ],
        popUpWindow1: '',
        popUpWindow2: '',
        popUpWindow1Link: '',
        popUpWindow2Link: '',
      },
      monthCoupon: {
        bg: '//img10.360buyimg.com/imgzone/jfs/t1/277610/32/15428/166141/67edfab7F1786e525/8e0426e0dcf6c410.jpg',
        prizeList: [],
        repeatHotZoneList: [],
      },
      oldRights: {
        repeatBg: '//img10.360buyimg.com/imgzone/jfs/t1/278466/3/14684/95965/67eca355Ff2e26f69/74169a0b1d5bafd8.jpg',
        repeatHotZoneList: [],
      },
      fanPrivilege: {
        repeatBg: '//img10.360buyimg.com/imgzone/jfs/t1/278809/18/13994/110986/67eca354Ffca7585f/08c1a252b05608db.jpg',
        repeatHotZoneList: [],
      },
      memberNotes: {
        repeatBg: '//img10.360buyimg.com/imgzone/jfs/t1/281950/17/13389/87848/67eca355F9706e20c/0dd5f8c301c3e711.jpg',
        repeatHotZoneList: [],
      },
      consume: {
        repeatBg: '//img10.360buyimg.com/imgzone/jfs/t1/276084/6/26797/10611/680b2bb9Fe02d0981/df726cf66229e915.png',
        upgradeImgList: [
          {
            notSelected:
              '//img10.360buyimg.com/imgzone/jfs/t1/282938/39/26457/6882/680b40c8F537001a6/02e6db5a3d102ece.png',
            select: '//img10.360buyimg.com/imgzone/jfs/t1/278260/19/27918/24918/680b415fF06029a57/f9ca1c0ff0eb9be6.png',
            prizeList: [],
            crowdValue: '',
          },
          {
            notSelected:
              '//img10.360buyimg.com/imgzone/jfs/t1/282938/39/26457/6882/680b40c8F537001a6/02e6db5a3d102ece.png',
            select: '//img10.360buyimg.com/imgzone/jfs/t1/278260/19/27918/24918/680b415fF06029a57/f9ca1c0ff0eb9be6.png',
            prizeList: [],
            crowdValue: '',
          },
          {
            notSelected:
              '//img10.360buyimg.com/imgzone/jfs/t1/282938/39/26457/6882/680b40c8F537001a6/02e6db5a3d102ece.png',
            select: '//img10.360buyimg.com/imgzone/jfs/t1/278260/19/27918/24918/680b415fF06029a57/f9ca1c0ff0eb9be6.png',
            crowdValue: '',
            prizeList: [],
          },
        ],
      },
    },
    pushAllowed: {
      memberCard: 0,
      consume: 0,
      monthCoupon: 0,
      oldRights: 0,
      fanPrivilege: 0,
      memberNotes: 0,
    },
  };
};
function updateModule(modules, selectedModule, payload) {
  if (Array.isArray(modules[selectedModule])) {
    return {
      ...modules,
      [selectedModule]: payload,
    };
  } else {
    return {
      ...modules,
      [selectedModule]: {
        // ...modules[selectedModule],
        ...payload,
      },
    };
  }
}
function updatePushAllowed(pushAllowed, selectedModule) {
  return {
    ...pushAllowed,
    [selectedModule]: pushAllowed[selectedModule] + 1,
  };
}
function resetPushAllowed(pushAllowed, selectedModule) {
  return {
    ...pushAllowed,
    [selectedModule]: 0,
  };
}

export function decorateReducer(state, action) {
  switch (action.type) {
    case 'SELECT_MODULE':
      return { ...state, selectedModule: action.payload };
    case 'UPDATE_MODULE':
      return {
        ...state,
        modules: updateModule(state.modules, state.selectedModule, action.payload),
        pushAllowed: updatePushAllowed(state.pushAllowed, state.selectedModule),
      };
    case 'RESET_PUSH': {
      return {
        ...state,
        pushAllowed: resetPushAllowed(state.pushAllowed, state.selectedModule),
      };
    }
    case 'INIT_MODULE': {
      return {
        ...state,
        modules: {
          ...state.modules,
          ...action.payload,
        },
      };
    }
    default:
      return state;
  }
}
