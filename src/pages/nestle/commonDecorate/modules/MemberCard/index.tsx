import React, { useState } from 'react';
import { Card, Button, Divider, Loading, Table, Message, Dialog } from '@alifd/next';
import styles from './index.module.scss';
import LzImageSelector from '@/components/LzImageSelector';
import LzColorPicker from '@/components/LzColorPicker';
import { memberUpdate } from '@/api/nestle';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrizeForDZ';

function MembershipCard({ data, dispatch, id }) {
  const [loading, setLoading] = React.useState(false);
  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = React.useState(null);
  // 当前编辑行index
  const [target, setTarget] = React.useState(0);
  // 当前编辑的表格
  const [tableName, setTableName] = React.useState('');
  // 编辑时最小发放数量
  const [sendTotalCountMin, setSendTotalCountMin] = React.useState(1);
  const [skuVisible, setSkuVisible] = React.useState(false);
  const [seriesSkuList, setSeriesSkuList] = React.useState([]);
  // 系列内总兑换次数最大值
  const [totalReceiveCountMax, setTotalReceiveCountMax] = React.useState(1);
  const saveSetting = (): any => {
    setLoading(true);
    memberUpdate({
      content: JSON.stringify(data),
      keyId: 'memberCard',
      prize: 1,
    })
      .then((res) => {
        Message.success('会员卡模块模块保存成功');
        dispatch({ type: 'RESET_PUSH' });
        setLoading(false);
      })
      .catch((e) => {
        Message.error(e.message);
        setLoading(false);
      });
  };

  const setData = (value) => {
    dispatch({ type: 'UPDATE_MODULE', payload: value });
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (prizeData): boolean | void => {
    const levelIndex = parseInt(tableName, 10);
    const updatedData = { ...data };
    // 只保证 prizeList 是数组
    if (!Array.isArray(updatedData.upgradeImgList[levelIndex].prizeList)) {
      updatedData.upgradeImgList[levelIndex].prizeList = [];
    }
    // 计算prizeLevel
    const prizeLevel = levelIndex + 3;
    if (editValue === null) {
      // 新增
      updatedData.upgradeImgList[levelIndex].prizeList.push({
        ...prizeData,
        prizeLevel,
      });
    } else {
      // 编辑
      updatedData.upgradeImgList[levelIndex].prizeList[target] = {
        ...prizeData,
        prizeLevel,
      };
    }
    setData(updatedData);
    setVisible(false);
  };
  const commonProps = {
    title: '会员卡模块',
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting}>
          保存模块设置
        </Button>
      </div>
    ),
  };
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <Loading visible={loading} style={{ width: '100%' }}>
            <div className={styles.operation}>
              <div className={styles.MemberContainer}>
                <div className="crm-label">背景设置</div>
                <div className={styles.imgUpload}>
                  <LzImageSelector
                    bgWidth={200}
                    bgHeight={140}
                    value={data.bg}
                    onChange={(bg) => {
                      const updatedData = { ...data };
                      updatedData.bg = bg;
                      setData(updatedData);
                    }}
                  />
                  <div className={styles.tip}>
                    {/* <div>图片尺寸：建议宽度为707px，高度为490px</div> */}
                    <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                  </div>
                </div>
              </div>
              <div className={styles.upgradeBtn}>
                <div className="crm-label">升级按钮配置</div>
                <div className={styles.imgUpload}>
                  <LzImageSelector
                    bgWidth={200}
                    bgHeight={140}
                    value={data.upgradeBtnBg}
                    onChange={(bg) => {
                      const updatedData = { ...data };
                      updatedData.upgradeBtnBg = bg;
                      setData(updatedData);
                    }}
                  />
                  <div className={styles.tip}>
                    {/* <div>图片尺寸：建议宽度为707px，高度为490px</div> */}
                    <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                  </div>
                </div>
                <div className={styles.btnLink}>
                  <span>请输入链接：</span>
                  <input
                    className={styles.input}
                    type="text"
                    value={data.upgradeBtnLink}
                    onChange={(e) => {
                      const updatedData = { ...data };
                      updatedData.upgradeBtnLink = e.target.value;
                      setData(updatedData);
                    }}
                  />
                </div>
              </div>
              <div className={styles.ProcessContainer}>
                <div className="crm-label">等级进度条配置</div>
                <div className={styles.colorPicker}>
                  <div>
                    <span>进度条凹槽颜色</span>
                    <LzColorPicker
                      value={data.processUnderColor}
                      onChange={(processUnderColor) => {
                        const updatedData = { ...data };
                        updatedData.processUnderColor = processUnderColor;
                        setData(updatedData);
                      }}
                    />
                  </div>
                  <div>
                    <span>进度条颜色</span>
                    <LzColorPicker
                      value={data.processColor}
                      onChange={(processColor) => {
                        const updatedData = { ...data };
                        updatedData.processColor = processColor;
                        setData(updatedData);
                      }}
                    />
                  </div>
                </div>
              </div>
              <div className={styles.UpgradeGiftContainer}>
                <div className="crm-label">升级礼配置</div>
                <div className={styles.upgradeList}>
                  <div>标题图片：</div>
                  <div className={styles.imgUpload}>
                    <LzImageSelector
                      bgWidth={200}
                      bgHeight={140}
                      value={data.upgradeTitleImg}
                      onChange={(bg) => {
                        const updatedData = { ...data };
                        updatedData.upgradeTitleImg = bg;
                        setData(updatedData);
                      }}
                    />
                    <div className={styles.tip}>
                      {/* <div>图片尺寸：建议宽度为707px，高度为490px</div> */}
                      <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                    </div>
                  </div>
                </div>
                <div className={styles.upgradeList}>
                  <div>背景图片：</div>
                  <div className={styles.imgUpload}>
                    <LzImageSelector
                      bgWidth={200}
                      bgHeight={140}
                      value={data.upgradeBg}
                      onChange={(bg) => {
                        const updatedData = { ...data };
                        updatedData.upgradeBg = bg;
                        setData(updatedData);
                      }}
                    />
                    <div className={styles.tip}>
                      {/* <div>图片尺寸：建议宽度为707px，高度为490px</div> */}
                      <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                    </div>
                  </div>
                </div>

                <div className={styles.upgradeList}>
                  {Array.isArray(data.upgradeImgList) &&
                    data.upgradeImgList.map((item, idx) => {
                      // item 形如 { '3': '图片地址' }
                      const level = Object.keys(item)[0];
                      const imgUrl = item[level];
                      const currentPrizeList = Array.isArray(item.prizeList) ? item.prizeList : [];

                      // 准备表格数据源，如果为空则添加一个空行
                      const tableDataSource =
                        currentPrizeList.length > 0
                          ? currentPrizeList
                          : [
                              {
                                prizeName: '',
                                prizeType: '',
                                numPerSending: null,
                                unitCount: null,
                                sendTotalCount: null,
                                unitPrice: null,
                                prizeImg: '',
                              },
                            ]; // 空数据行结构
                      return (
                        <div className={styles.giftImg} key={level}>
                          <div>{`LV${level}奖品配置：`}</div>
                          <LzImageSelector
                            bgWidth={120}
                            bgHeight={120}
                            value={imgUrl}
                            onChange={(bg) => {
                              const updatedData = { ...data };
                              // 更新对应等级的图片
                              updatedData.upgradeImgList = data.upgradeImgList.map((imgObj, i) =>
                                i === idx ? { [level]: bg } : imgObj,
                              );
                              setData(updatedData);
                            }}
                          />
                          <Table dataSource={tableDataSource} style={{ marginLeft: '15px' }}>
                            <Table.Column title="奖品名称" dataIndex="prizeName" />
                            <Table.Column
                              title="单位数量"
                              cell={(_, idx, row) => {
                                if (row.prizeType === 1) {
                                  return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                                } else {
                                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                                }
                              }}
                            />
                            <Table.Column
                              title="发放份数"
                              cell={(_, idx, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                            />
                            <Table.Column
                              title="奖品图"
                              cell={(_, idx, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                            />
                            <Table.Column
                              title="操作"
                              width={130}
                              // val: 当前单元格值, prizeIdx: 当前行索引 (0-based), row: 当前行数据
                              // idx: 外层 map 的索引，代表等级索引 (0-based)
                              cell={(val, prizeIdx, row) => (
                                <>
                                  <Button
                                    text
                                    type="primary"
                                    onClick={() => {
                                      if (row.prizeName) {
                                        // --- 编辑逻辑 ---
                                        setEditValue(row); // 设置编辑数据为当前行
                                        setTarget(prizeIdx); // 设置目标行为当前奖品索引
                                        setTableName(idx.toString()); // 设置目标表格为当前等级索引
                                        // setSendTotalCountMin(...); // 如果需要设置最小发放数，逻辑需要调整
                                        setVisible(true); // 打开奖品选择弹窗
                                      } else {
                                        // --- 新增逻辑 ---
                                        setEditValue(null); // 清空编辑数据，表示新增
                                        // 获取当前等级实际的奖品列表，计算新奖品的索引
                                        const actualPrizeList = Array.isArray(data.upgradeImgList[idx]?.prizeList)
                                          ? data.upgradeImgList[idx].prizeList
                                          : [];
                                        setTarget(actualPrizeList.length); // 目标索引为列表末尾
                                        setTableName(idx.toString()); // 设置目标表格为当前等级索引
                                        setVisible(true); // 打开奖品选择弹窗
                                      }
                                    }}
                                  >
                                    {/* 根据是否有奖品名显示不同图标和文字 */}
                                    <i className={`iconfont ${row.prizeName ? 'icon-bianji' : 'icon-add'}`} />{' '}
                                    {row.prizeName ? '编辑' : '新增'}
                                  </Button>

                                  {/* 只有当行内有数据时才显示删除按钮 */}
                                  {row.prizeName && (
                                    <Button
                                      text
                                      type="primary"
                                      style={{ marginLeft: '5px' }} // 添加一些间距
                                      onClick={() => {
                                        // --- 删除逻辑 ---
                                        Dialog.confirm({
                                          v2: true,
                                          title: '提示',
                                          centered: true,
                                          content: '确认删除该奖品吗？',
                                          onOk: () => {
                                            const updatedData = { ...data };
                                            // 确保 prizeList 存在且为数组
                                            if (Array.isArray(updatedData.upgradeImgList[idx]?.prizeList)) {
                                              // 从当前等级的 prizeList 中移除指定索引的奖品
                                              updatedData.upgradeImgList[idx].prizeList.splice(prizeIdx, 1);
                                              // 更新状态
                                              setData(updatedData);
                                            }
                                          },
                                          onCancel: () => console.log('取消删除'),
                                        });
                                      }}
                                    >
                                      <i className={`iconfont icon-shanchu`} /> 删除
                                    </Button>
                                  )}
                                </>
                              )}
                            />
                          </Table>
                        </div>
                      );
                    })}
                </div>
              </div>
              <div className={styles.MemberContainer}>
                <div className="crm-label">弹窗设置</div>
                <div className={styles.imgUpload}>
                  <LzImageSelector
                    bgWidth={200}
                    bgHeight={140}
                    value={data.popUpWindow1}
                    onChange={(bg) => {
                      const updatedData = { ...data };
                      updatedData.popUpWindow1 = bg;
                      setData(updatedData);
                    }}
                  />
                  <div className={styles.tip}>
                    {/* <div>图片尺寸：建议宽度为707px，高度为490px</div> */}
                    <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                    <div className={styles.input1}>
                      请输入链接：
                      <input
                        className={styles.input}
                        type="text"
                        value={data.popUpWindow1Link}
                        onChange={(e) => {
                          const updatedData = { ...data };
                          updatedData.popUpWindow1Link = e.target.value;
                          setData(updatedData);
                        }}
                      />
                    </div>
                  </div>
                </div>
                <div className={styles.imgUpload} style={{ marginTop: '10px' }}>
                  <LzImageSelector
                    bgWidth={200}
                    bgHeight={140}
                    value={data.popUpWindow2}
                    onChange={(bg) => {
                      const updatedData = { ...data };
                      updatedData.popUpWindow2 = bg;
                      setData(updatedData);
                    }}
                  />
                  <div className={styles.tip}>
                    {/* <div>图片尺寸：建议宽度为707px，高度为490px</div> */}
                    <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                    <div className={styles.input2}>
                      请输入链接：
                      <input
                        className={styles.input}
                        type="text"
                        value={data.popUpWindow2Link}
                        onChange={(e) => {
                          const updatedData = { ...data };
                          updatedData.popUpWindow2Link = e.target.value;
                          setData(updatedData);
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Loading>
        </Card.Content>
      </Card>
      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          formData={data}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={() => setVisible(false)}
          hasProbability={false}
          hasLimit={false}
          typeList={[2, 3, 8]}
          defaultTarget={3}
        />
      </LzDialog>
    </div>
  );
}

export default MembershipCard;
