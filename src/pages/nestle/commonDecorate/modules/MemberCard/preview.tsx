import React from 'react';
import styles from './index.module.scss';
import 'swiper/scss';
import 'swiper/css/effect-coverflow';
import background from '@/pages/babyCare/assets/background';

export default ({ data }) => {
  const currentLevel = 3;
  const levels = ['萌新妈妈', '营养妈妈', '全能妈妈', '金牌妈妈', '钻石妈妈'];
  // 定义背景图片路径
  const levelImages = [
    '//img10.360buyimg.com/imgzone/jfs/t1/278338/6/14080/739/67eba710Ffaf1e8ef/dbc602b67540c2f7.png',
    '//img10.360buyimg.com/imgzone/jfs/t1/277568/28/14448/1045/67eba710F3f8bcdc4/e43db4e39163dd97.png',
  ];
  return (
    <div className={styles.preview}>
      <img src={data.bg} className={styles.bg} alt="" />
      <img
        className={styles.btn2}
        src="//img10.360buyimg.com/imgzone/jfs/t1/270658/31/28549/3021/680b860dF2c898ed9/e43197e20a066ad8.png"
        alt=""
      />
      <div className={styles.user}>
        <div className={styles.avatar} />
        <div className={styles.info}>
          <div style={{ display: 'flex' }}>
            <div className={styles.name}>
              尊敬的会员：<span className={styles.infoFont}>Anson</span>
            </div>
            <div className={styles.level}>
              当前会员等级：<span className={styles.infoFont}>LV0</span>
            </div>
          </div>
          <div style={{ display: 'flex' }}>
            <div className={styles.days}>
              感谢你陪伴：<span>109天</span>
            </div>
            <div className={styles.points}>
              我的积分：<span className={styles.infoFont}>1000</span>
            </div>
          </div>
        </div>
      </div>
      <img src={data.upgradeBtnBg} className={styles.btnArea} alt="" />
      <div className={styles.process}>
        <div className={styles.processContent}>
          <div
            className={styles.processBg}
            style={{
              background: data.processUnderColor,
            }}
          />
          <div
            className={styles.processMain}
            style={
              {
                background: data.processColor,
                '--process': currentLevel,
              } as any
            }
          />
          <div className={styles.processCoin}>
            {levels.map((level, index) => {
              return (
                <div
                  key={index}
                  className={styles.processCoinDot}
                  style={
                    {
                      backgroundImage: `url(${levelImages[currentLevel - 1 >= index ? 0 : 1]})`,
                      '--levelName': `"${level}"`,
                    } as any
                  }
                />
              );
            })}
          </div>
        </div>
      </div>
      <img className={styles.upgradeTitleImg} src={data.upgradeTitleImg} alt="" />
      <img className={styles.tipImg} src={data.tipImg} alt="" />
      <div
        className={styles.upgradeImgList}
        style={{
          backgroundImage: `url(${data.upgradeBg})`,
        }}
      >
        {/* 确保 data.upgradeImgList 是数组再进行 map */}
        {Array.isArray(data.upgradeImgList) &&
          data.upgradeImgList.map((item, index) => {
            // 从 item 对象中获取等级和图片 URL
            const level = Object.keys(item)[0]; // 例如 '3', '4', '5'
            const imgUrl = item[level]; // 获取对应的图片地址
            return (
              // 将 key 属性添加到 map 返回的最外层元素 div 上
              // 使用 level 作为 key，如果 level 可能重复或无效，则使用 index 作为备选
              <div className={styles.upgradeItem} key={index}>
                {/* 使用正确的 imgUrl */}
                <img className={styles.itemImg} src={imgUrl} />
                <div>限量{item.prizeList[0].sendTotalCount}份</div>
                <img
                  className={styles.btn}
                  src="//img10.360buyimg.com/imgzone/jfs/t1/273856/38/26226/3651/6809d6bdF22e0bca1/d9e203b0a85eb291.png"
                />
              </div>
            );
          })}
      </div>
      <img
        className={styles.tip}
        src="//img10.360buyimg.com/imgzone/jfs/t1/250699/36/32263/9259/6809d99eF44123d9a/ad1c6615cc655cad.png"
        alt=""
      />
    </div>
  );
};
