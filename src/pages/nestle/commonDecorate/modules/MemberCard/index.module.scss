.preview {
  position: relative;

  .bg {
    width: 100%;
    display: block;
  }
  .btn2 {
    position: absolute;
    width: 90px;
    top: 595px;
    right: 10px;
  }

  .icon {
    position: absolute;
    right: 0;
    top: 30px;
    display: flex;
    flex-direction: column;

    > img {
      width: 70px;
      margin-bottom: 10px;
    }
  }

  .user {
    position: absolute;
    display: flex;
    align-items: center;
    top: 435px;
    left: 18px;

    .avatar {
      width: 55px;
      height: 55px;
      background: #eae5fa;
      border: solid 1px #7739ab;
      border-radius: 50%;
      margin-right: 10px;
    }

    .name,
    .level,
    .days,
    .points {
      width: 150px;
      font-size: 12px;
      font-weight: bold;
      font-stretch: normal;
      letter-spacing: 0;
      margin-bottom: 3px;
      color: #794dab;

      .infoFont {
        font-weight: bolder;
        font-family: Arial-Black;
        font-style: italic;
      }
    }
  }
  .btnArea {
    width: 200px;
    position: absolute;
    top: 495px;
    left: 20px;
  }
  .process {
    position: absolute;
    top: 527px;
    width: 100%;
    padding: 0 16px;

    .line {
      position: absolute;
      height: 6px;
      width: 100%;
      border-radius: 5px;
      top: 0;
      left: 0;
      border: solid 1px #dba008;
    }

    .processContent {
      position: relative;

      .processBg,
      .processMain {
        @extend .line;
      }

      .processMain {
        width: calc(20% * var(--process));
      }

      .processCoin {
        width: 100%;
        position: absolute;
        height: 9px;
        top: -2px;
        left: 0;
        display: flex;
        justify-content: space-around;
        align-items: center;

        .processCoinDot {
          width: 12px;
          height: 17px;
          position: relative;
          background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/277568/28/14448/1045/67eba710F3f8bcdc4/e43db4e39163dd97.png');
          background-repeat: no-repeat;
          background-size: 100%;

          &:before {
            content: var(--levelName);
            font-weight: bolder;
            font-family: Arial-Black;
            color: #794dab;
            font-style: italic;
            font-size: 10px;
            position: absolute;
            width: 40px;
            top: 20px;
            right: -13px;
          }
        }
      }

      .processPointIcon {
        width: 15px;
        height: 15px;
        position: absolute;
        left: -5px;
        top: -5px;
      }
    }
  }
  .upgradeTitleImg {
    width: 86%;
    position: absolute;
    top: 570px;
    left: 7%;
  }
  .tipImg {
    width: 40%;
    position: absolute;
    top: 600px;
    left: 30%;
  }
  .upgradeImgList {
    width: 92%;
    height: 143px;
    background-repeat: no-repeat;
    background-size: 100%;
    position: absolute;
    top: 620px;
    left: 4%;
    display: flex;
    padding-top: 5px;
    justify-content: space-around;
    .upgradeItem {
      width: 100px;
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 10px;
      color: #66239c;
      .itemImg {
        width: 100%;
        object-fit: contain;
        margin-bottom: 5px;
      }
      .btn {
        width: 70px;
      }
    }
  }
  .tip {
    width: 86%;
    position: absolute;
    top: 785px;
    left: 7%;
    object-fit: contain;
  }

  .swiper {
    position: absolute;
    bottom: 22px;
    padding: 0 32px;
    width: 100%;
    height: 70px;

    .previewItem {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 10px;
      color: #1e3932;
      user-select: none;
      text-align: center;

      > div {
        margin-top: 5px;
        transform: scale(0.85);
        width: 100px;
      }
    }
  }
}

.operation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .container {
    padding: 15px;
    background: #f4f6f9;
    border-radius: 5px;
  }

  .MemberContainer {
    @extend .container;

    .imgUpload {
      display: flex;
      align-items: center;
      gap: 10px;

      .tip {
        margin-top: 10px;
        .input1 {
          margin-top: 30px;
          .input {
            width: 300px;
          }
        }
        .input2 {
          margin-top: 30px;
          .input {
            width: 300px;
          }
        }
      }

      .removeHotZone {
        margin-top: 4px;

        i {
          margin-left: 5px;
          font-size: 12px;
          cursor: pointer;

          &:hover {
            color: #1677ff;
          }
        }
      }
    }
  }

  .EquityContainer {
    @extend .container;

    .imgUpload {
      gap: 10px;
      display: flex;
      align-items: center;
    }
  }
  .upgradeBtn {
    @extend .container;
    .imgUpload {
      display: flex;
      align-items: center;
      gap: 10px;

      .tip {
        margin-top: 10px;
      }

      .removeHotZone {
        margin-top: 4px;

        i {
          margin-left: 5px;
          font-size: 12px;
          cursor: pointer;

          &:hover {
            color: #1677ff;
          }
        }
      }
    }

    .btnLink {
      margin-top: 10px;
      .input {
        border: 1px solid #d9d9d9;
        width: 300px;
        height: 20px;
        background: none;
      }
    }
  }

  .ProcessContainer {
    @extend .container;

    .colorPicker {
      display: flex;
      margin-top: 10px;
      gap: 20px;

      span {
        font-weight: bold;
        margin-right: 10px;
      }
    }
  }
  .UpgradeGiftContainer {
    @extend.container;
    .imgUpload {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .upgradeList {
      margin-top: 10px;
      .giftImg {
        height: 120px;
        display: flex;
        align-items: center;
        margin-bottom: 10px;
      }
    }
  }
}
