import { getPhysicalCommodityPageListAll, getResPrizeSku } from '@/api/prize';
import LzImageSelector from '@/components/LzImageSelector';
import { activityEditDisabled, deepCopy, numRegularCheckInt } from '@/utils';
import format from '@/utils/format';
import { Button, Dialog, Field, Form, Grid, Input, Message, NumberPicker, Radio } from '@alifd/next';
import React, { useEffect, useReducer, useState } from 'react';
import delIcon from '../assets/del-icon.png';
import Plan from './components/Plan';
import styles from './index.module.scss';

interface ComponentProps {
  [propName: string]: any;
}

// eslint-disable-next-line complexity
const PropertyJdProduct = ({
  editValue,
  editDefaultValue,
  onChange,
  onCancel,
  planList = [],
  hasProbability = true,
  hasLimit = true,
  width,
  height,
  prizeKeyList,
  isEdit,
}: ComponentProps) => {
  const planImg = require('../assets/4.jpg');
  const defaultValue = {
    prizeKey: null,
    prizeType: 3,
    dayLimitType: 1,
    dayLimit: 1,
    prizeImg: '',
  };
  const [prizeData, setPrizeData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || defaultValue);
  const field = Field.useField();

  const [winJdShow, setWinJdShow] = useState(false); // 京豆详情
  const [isDeletedReselect, setIsDeletedReselect] = useState(false); // 是否删除重选
  // 发放份数/单次发放量
  const setData = (data: any) => {
    setPrizeData({
      ...prizeData,
      ...data,
    });
  };
  const submit = (values: any, errors: any): boolean | void => {
    // let { quantityAvailable } = deepCopy(prizeData);
    // // 如果没有删除重选，就取回显值的可用库存。如果删除重选了，就用重选的可用库存
    // if (!isDeletedReselect) {
    //   quantityAvailable = editValue?.quantityAvailable;
    // }
    if (prizeData.sendTotalCount > prizeData.quantityAvailable) {
      Message.error(`发放份数不能大于可用库存`);
      return false;
    }
    if (hasLimit && prizeData.dayLimitType === 2 && prizeData.sendTotalCount < prizeData.dayLimit) {
      Message.error(`每日发放限额份数不能大于发放总份数，请重新设置`);
      return false;
    }
    if (hasProbability && +prizeData.probability <= 0) {
      Message.error(`中奖概率必须大于0`);
      return false;
    }
    !errors && onChange(prizeData);
  };
  const delPlan = async () => {
    const data = { ...prizeData };
    data.prizeKey = null;
    setPrizeData(data);
    // 删除计划
    if (planList.indexOf(prizeData.skuCode) > -1) {
      planList.splice(planList.indexOf(prizeData.skuCode), 1);
    }
  };
  const onSubmit = (resource: any): any => {
    if (!resource) {
      return;
    }
    resource.prizeKey = resource.skuCode;
    setPrizeData({ ...resource, prizeImg: resource.skuMainPicture });
    setWinJdShow(false);
    field.setErrors({ prizeKey: '' });
  };
  const prizeFormLayout: any = {
    labelCol: {
      fixedSpan: 5,
    },
    colon: true,
  };

  // React.useEffect(() => {
  //   if (editValue) {
  //     getResPrizeSku({ skuCode: prizeData.skuCode }).then((res) => {
  //       if (res) {
  //         const data = { ...prizeData };
  //         data.quantityAvailable = res.quantityStock;
  //         setPrizeData(data);
  //       }
  //     });
  //   }
  // }, []);

  useEffect(() => {
    if (editValue) {
      getPhysicalCommodityPageListAll({ skuOrWmsCode: editValue.skuCode, pageNum: 1, pageSize: 10 }).then((data) => {
        if (data?.records) {
          const prize = data?.records[0] ?? null;
          if (prize) {
            setData({
              quantityAvailable:
                Number(prize.quantityTotal) - Number(prize.quantityFreeze) + (editDefaultValue?.sendTotalCount ?? 0),
            });
          }
        }
      });
    }
  }, []);

  return (
    <div className={styles.PropertyJdProduct}>
      <Form field={field} {...prizeFormLayout}>
        <Form.Item style={{ paddingTop: '15px' }} required requiredMessage="请选择实物">
          <Input htmlType="hidden" name="prizeKey" value={prizeData.prizeKey} />
          {!prizeData.prizeKey && (
            <div className={styles.selectActivity} style={{ marginTop: 10 }} onClick={() => setWinJdShow(true)}>
              <img style={{ width: '35%' }} src={planImg} alt="" />
            </div>
          )}
          {!!prizeData.prizeKey && (
            <div className={styles.beanPrizeBg}>
              {!isEdit && (
                <div
                  onClick={() => delPlan()}
                  style={{ backgroundImage: `url(${delIcon})` }}
                  className={styles.delIcon}
                />
              )}
              <div style={{ width: 210 }}>
                <p>商品名称：{prizeData.skuName}</p>
                <p>商品编号：{prizeData.skuCode}</p>
                <p>创建时间：{format.formatDateTimeDayjs(prizeData.createTime)}</p>
              </div>
              <div style={{ paddingLeft: 60 }}>
                <p>商家内部编号：{prizeData.wmsCode || '-'}</p>
                <p>可用库存：{prizeData.quantityAvailable}</p>
              </div>
            </div>
          )}
        </Form.Item>
        <Form.Item label="单份价值" required requiredMessage="请输入单份价值">
          <NumberPicker
            className={styles.formNumberPicker}
            onChange={(unitPrice: any) => setData({ unitPrice, unitCount: 1 })}
            name="unitPrice"
            type="inline"
            min={0}
            max={9999999}
            precision={2}
            value={prizeData.unitPrice}
          />
          元
        </Form.Item>
        <Form.Item label="发放份数" required requiredMessage="请输入发放份数" validator={numRegularCheckInt}>
          <NumberPicker
            className={styles.formNumberPicker}
            type="inline"
            min={1}
            max={999999999}
            step={1}
            name="sendTotalCount"
            value={prizeData.sendTotalCount}
            onChange={(sendTotalCount) => setData({ sendTotalCount })}
          />
          份
        </Form.Item>
        {hasProbability && (
          <Form.Item label="中奖概率" required requiredMessage="请输入中奖概率">
            <Input
              name="probability"
              value={prizeData.probability}
              addonTextAfter="%"
              onChange={(probability) => {
                if (probability) {
                  const regex = /^(\d|[1-9]\d|100)(\.\d{0,3})?$/;
                  if (regex.test(probability)) {
                    setData({ probability });
                  }
                } else {
                  setData({ probability: '' });
                }
              }}
              className={styles.formInputCtrl}
            />
          </Form.Item>
        )}
        {hasLimit && (
          <Form.Item label="每日限额" required requiredMessage="请输入每日限额">
            {prizeData.dayLimitType === 2 && <Input htmlType="hidden" name="dayLimit" value={prizeData.dayLimit} />}
            <div>
              <Radio.Group
                defaultValue={prizeData.dayLimitType || 1}
                onChange={(dayLimitType) => setData({ dayLimitType })}
              >
                <Radio id="1" value={1}>
                  不限制
                </Radio>
                <Radio id="2" value={2}>
                  限制
                </Radio>
              </Radio.Group>
              {prizeData.dayLimitType === 2 && (
                <div className={styles.panel}>
                  <NumberPicker
                    value={prizeData.dayLimit}
                    onChange={(dayLimit) => {
                      setData({ dayLimit });
                      field.setErrors({ dayLimit: '' });
                    }}
                    type="inline"
                    min={1}
                    max={9999999}
                    className={styles.number}
                  />
                  份
                </div>
              )}
            </div>
          </Form.Item>
        )}
        <Form.Item label="奖品名称" required requiredMessage="请输入奖品名称">
          <Input
            className={styles.formInputCtrl}
            maxLength={10}
            showLimitHint
            placeholder="请输入奖品名称"
            name="prizeName"
            trim
            value={prizeData.prizeName}
            onChange={(prizeName) => setData({ prizeName })}
          />
        </Form.Item>
        <Form.Item label="奖品图片" required requiredMessage="请上传奖品图片">
          <Input htmlType="hidden" name="prizeImg" value={prizeData.prizeImg} />
          <Grid.Row>
            <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
              <LzImageSelector
                width={width}
                height={height}
                value={prizeData.prizeImg}
                onChange={(prizeImg) => {
                  setData({ prizeImg });
                  field.setErrors({ prizeImg: '' });
                }}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <div className={styles.tip}>
                <p>
                  图片尺寸：{width}px*{height || '任意'}px
                </p>
                <p>图片大小：不超过1M</p>
                <p>图片格式：JPG、JPEG、PNG</p>
              </div>
              <div>
                <Button
                  type="primary"
                  text
                  onClick={() => {
                    setData({ prizeImg: '' });
                  }}
                >
                  重置
                </Button>
              </div>
            </Form.Item>
          </Grid.Row>
        </Form.Item>
        <Form.Item>
          <Grid.Row>
            <Grid.Col style={{ textAlign: 'right' }}>
              <Form.Submit className="form-btn" validate type="primary" onClick={submit}>
                确认
              </Form.Submit>
              <Form.Reset className="form-btn" onClick={onCancel}>
                取消
              </Form.Reset>
            </Grid.Col>
          </Grid.Row>
        </Form.Item>
      </Form>
      <Dialog
        title="选择实物"
        footer={false}
        shouldUpdatePosition
        visible={winJdShow}
        onClose={() => setWinJdShow(false)}
      >
        <Plan prizeKeyList={prizeKeyList} onSubmit={onSubmit} />
      </Dialog>
    </div>
  );
};

export default PropertyJdProduct;
