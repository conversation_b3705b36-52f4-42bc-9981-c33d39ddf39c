// Swisse奖品管理 - 奖品维护;
import React, { useState, useEffect } from 'react';
import {
  Field,
  Form,
  Dialog,
  Box,
  Input,
  DatePicker2,
  Tab,
  Button,
  NumberPicker,
  Loading,
  Select,
  Grid,
  Badge,
  Tag,
  Message,
  Balloon,
  Icon,
  CascaderSelect,
  Radio,
} from '@alifd/next';
import LzMsg from '@/components/LzMsg';
import Uploader from './Uploader/Uploader';
import {
  deliveryInfoQueryGetInfoPage,
  deliveryPrizeGetChannelData,
  deliveryInfoEnterExport,
  deliveryPrizeGetPrizeData,
  deliveryInfoEnterGetImportInfo,
  deliveryInfoEnterCheckImport,
  deliveryInfoEnterImport,
  deliveryInfoEnterCreateInfo,
  deliveryInfoQueryExceptionStatus,
  deliveryInfoEnterViewRepeatInfo,
  deliveryInfoQueryUpdateInfo,
  deliveryInfoEnterBatchDelete,
  deliveryInfoEnterBatchImportRepeatInfo,
  deliveryInfoErrorInfoBatchUpdatePrize,
} from '@/api/swisse';
import LzPanel from '@/components/LzPanel';
import dayjs from 'dayjs';
import { downloadExcel } from '../../../../utils';
import CustomTable from '../CustomTable';
import type { ColumnConfig, CustomTableConfig } from '../CustomTable';
import type {
  IPageSwisseDeliveryGetQueryInfoPageResponse,
  SwisseDeliveryGetQueryInfoPageResponse,
  IPageBdSwisseDeliveryPrize,
  BdSwisseDeliveryPrize,
  IPageSwisseDeliveryEnterInfoTemplate,
  SwisseDeliveryEnterInfoTemplate,
  SwisseDeliveryEnterCheckImportResponse,
  IPageBdSwisseDeliveryChannel,
} from '@/api/types';
import { prizeTypeList, shopList, defaultColumns } from '../publicData';
import smart from 'address-smart-parse';

const RadioGroup = Radio.Group;
const addressInfoLabel = (
  <span>
    {'智能粘贴：'}
    <Balloon type="primary" trigger={<Icon type="help" style={{ color: 'blue' }} size="small" />} closable={false}>
      示例：张三 18888888888 北京市海淀区中关村软件园X号楼XX室(每个关键字之间空格间隔 可缺少关键字)
    </Balloon>
  </span>
);
import { areaList } from '../area';

const FormItem = Form.Item;

const { Row, Col } = Grid;

interface Pager {
  pageNum: number;
  pageSize: number;
  total: number;
}

const initPager: Pager = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
export default () => {
  const field = Field.useField();
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<SwisseDeliveryGetQueryInfoPageResponse[]>([]);
  const [channelList, setChannelList] = useState<any[]>([]);
  const [pageInfo, setPageInfo] = useState<Pager>(initPager);
  const [infoStatus, setInfoStatus] = useState(0); // 信息状态 0-已录入 1-重复 2-异常
  const [hasNewErr, setHasNewErr] = useState(false); // 是否有新异常信息
  const [columns] = useState<ColumnConfig[]>(defaultColumns);

  const [provinceCityDistrict, setProvinceCityDistrict] = useState<string>('');

  const customTableConfig = {
    primaryKey: 'infoId',
    dataSource: list,
    pageInfo,
    columns,
    handlePage: (p: Pager) => {
      const { pageNum, pageSize } = p;
      handlePage({ pageNum, pageSize });
    },
  };
  const [selectedInfoIds, setSelectedInfoIds] = useState<string[]>([]);
  const RepetitiveTableConfig: CustomTableConfig = {
    ...customTableConfig,
    columns: [
      ...defaultColumns,
      {
        title: '操作',
        align: 'center',
        lock: 'right',
        width: 150,
        cell: (value, index, record: SwisseDeliveryGetQueryInfoPageResponse) => {
          return (
            <Button type="primary" onClick={() => getRepetitiveList(record.infoId!)}>
              查看重复信息
            </Button>
          );
        },
      },
    ],
    rowSelection: {
      onChange: function (selected, records) {
        setSelectedInfoIds(selected);
      },
      selectedRowKeys: selectedInfoIds,
    },
  };
  const abnormalTableConfig: CustomTableConfig = {
    ...customTableConfig,
    columns: [
      ...defaultColumns,
      {
        title: '异常原因',
        dataIndex: 'remark',
        align: 'center',
        width: 150,
      },
      {
        title: '操作',
        align: 'center',
        lock: 'right',
        width: 150,
        cell: (value, index, record: SwisseDeliveryGetQueryInfoPageResponse) => {
          return (
            <Button type="primary" disabled={!record.prizeTypeName} onClick={() => editAbnormalInfo(record)}>
              编辑
            </Button>
          );
        },
      },
    ],
    rowSelection: {
      onChange: function (selected, records) {
        setSelectedInfoIds(selected);
      },
      selectedRowKeys: selectedInfoIds,
    },
  };
  const getChanelList = debounce(async (channel: string = '') => {
    const postData = {
      pageNum: 1,
      pageSize: 100000,
      channel,
    };
    try {
      setLoading(true);
      const result = (await deliveryPrizeGetChannelData(postData)) as IPageBdSwisseDeliveryChannel;
      setChannelList(result.records || []);
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  }, 300);
  useEffect(() => {
    getChanelList();
  }, []);
  useEffect(() => {
    setSelectedInfoIds([]);
    getDataList();
    infoStatus !== 2 && getHasNewErr();
  }, [infoStatus]);
  const handleChangeTab = (status: number) => {
    setInfoStatus(status);
  };
  const handlePage = ({ pageSize, pageNum }) => {
    getDataList({ total: 0, pageSize, pageNum });
  };
  const getDataList = async (page: Pager = initPager) => {
    const formValues = field.getValues() as {
      winningTime: string[];
      uploadTime: string[];
    };
    const postData = {
      ...page,
      infoStatus,
      ...formValues,
      sortStatus: 1,
      startDate: formValues.winningTime && dayjs(formValues.winningTime[0]).format('YYYY-MM-DD'),
      endDate: formValues.winningTime && dayjs(formValues.winningTime[1]).format('YYYY-MM-DD'),
      uploadStartDate: formValues.uploadTime && dayjs(formValues.uploadTime[0]).format('YYYY-MM-DD'),
      uploadEndDate: formValues.uploadTime && dayjs(formValues.uploadTime[1]).format('YYYY-MM-DD'),
    };
    try {
      setLoading(true);
      const result = (await deliveryInfoQueryGetInfoPage(postData)) as IPageSwisseDeliveryGetQueryInfoPageResponse;
      setList(result.records || []);
      setPageInfo({
        ...page,
        total: Number(result.total),
      });
      //   如果查询到异常信息，且当前状态为异常，则取消小红点
      if (infoStatus === 2 && result?.records?.length) {
        setHasNewErr(false);
      }
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    setPageInfo({ ...pageInfo, pageNum: 1, total: 0 });
    getDataList();
  };
  const handleExport = async () => {
    setIsSelectPrizeVisible(true);
  };
  //   导出模板选择奖品
  const [isSelectPrizeVisible, setIsSelectPrizeVisible] = useState(false);
  const prizeField = Field.useField();
  const [prizeLst, setPrizeLst] = useState<BdSwisseDeliveryPrize[]>([]);
  const [prizePageInfo, setPrizePageInfo] = useState<Pager>(initPager);
  const [tableSelectedRecords, setTableSelectedRecords] = useState<BdSwisseDeliveryPrize[]>([]);
  const [selectedprizesList, setSelectedprizesList] = useState<BdSwisseDeliveryPrize[]>([]);
  const [searchInfo, setSearchInfo] = useState({});
  const [isAddInfoVisible, setIsAddInfoVisible] = useState(false);

  const getPrizeList = async () => {
    if (!isSelectPrizeVisible) {
      return;
    }
    const postData = {
      ...prizePageInfo,
      ...searchInfo,
    };
    try {
      setLoading(true);
      const result = (await deliveryPrizeGetPrizeData(postData)) as IPageBdSwisseDeliveryPrize;
      setPrizeLst(result.records || []);
      const prizeNameList = result.records?.map((item) => {
        return { label: item.prizeName!, value: item.prizeName!, prizeType: item.prizeType! };
      }) as any[];
      setPrizeNameList(prizeNameList || []);
      setPrizePageInfo({
        ...prizePageInfo,
        total: Number(result.total),
      });
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (isSelectPrizeVisible || isAddInfoVisible) {
      getPrizeList();
    }
  }, [isSelectPrizeVisible, prizePageInfo.pageNum, prizePageInfo.pageSize, searchInfo, isAddInfoVisible]);
  const prizeDialogTableConfig: CustomTableConfig = {
    dataSource: prizeLst,
    pageInfo: prizePageInfo,
    columns: [
      {
        title: '奖品名称',
        dataIndex: 'prizeName',
        align: 'center',
      },
      {
        title: '奖品类型',
        dataIndex: 'prizeType',
        align: 'center',
        cell: (value) => {
          return prizeTypeList.find((item) => item.value === value)!.label;
        },
      },
      {
        title: '奖品价值',
        dataIndex: 'prizeValue',
        align: 'center',
      },
      {
        //   title: '库存总量(份)',
        title: () => {
          return (
            <>
              库存总量(份)
              <Balloon.Tooltip v2 trigger={<Icon type="help" size="small" />} align="t">
                库存总量(份)=总数量
              </Balloon.Tooltip>
            </>
          );
        },
        dataIndex: 'totalCount',
        align: 'center',
        width: 100,
      },
      {
        //   title: '已发货数量(份)',
        title: () => {
          return (
            <>
              已发货数量(份)
              <Balloon.Tooltip v2 trigger={<Icon type="help" size="small" />} align="t">
                发货数量(份)=已发货和等待发货的合计
              </Balloon.Tooltip>
            </>
          );
        },
        dataIndex: 'sendCount',
        align: 'center',
        width: 100,
      },
      {
        //   title: '待发货数量(份)',
        title: () => {
          return (
            <>
              待发货数量(份)
              <Balloon.Tooltip v2 trigger={<Icon type="help" size="small" />} align="t">
                待发货数量(份)=已导入数量-已发货数量(份)
              </Balloon.Tooltip>
            </>
          );
        },
        dataIndex: 'totalCount',
        align: 'center',
        width: 100,
        cell: (value, index, record: BdSwisseDeliveryPrize) => {
          return record.importCount! - record.sendCount!;
        },
      },
      {
        //   title: '可用库存(份)',
        title: () => {
          return (
            <>
              可用库存(份)
              <Balloon.Tooltip v2 trigger={<Icon type="help" size="small" />} align="t">
                可用库存(份)=库存总量(份)-已导入数量
              </Balloon.Tooltip>
            </>
          );
        },
        dataIndex: 'totalCount',
        align: 'center',
        width: 100,
        cell: (value, index, record: BdSwisseDeliveryPrize) => {
          return record.totalCount! - record.importCount!;
        },
      },
    ],
    handlePage: (p: Pager) => {
      const { pageNum, pageSize } = p;
      setPrizePageInfo({ ...prizePageInfo, pageNum, pageSize });
    },
    rowSelection: {
      onSelect: function (selected, record, records) {
        setTableSelectedRecords(records);
      },
      onSelectAll: function (selected, records) {
        setTableSelectedRecords(records);
      },
    },
  };
  const queryPrize = () => {
    const prizeFormValues = prizeField.getValues();
    setSearchInfo(prizeFormValues);
    setPrizePageInfo({ ...prizePageInfo, pageNum: 1, total: 0 });
  };
  const exportTemplateByPrizes = async () => {
    if (!selectedprizesList.length) {
      LzMsg.error('请先选择奖品~');
      return;
    }
    console.log({
      prizeNames: selectedprizesList.map((item) => item.prizeName).join(','),
    });
    try {
      setLoading(true);
      const result: any = await deliveryInfoEnterExport({
        prizeNames: selectedprizesList.map((item) => item.prizeName).join(','),
      });
      downloadExcel(result, '信息录入模板（根据选择奖品导出）');
      setIsSelectPrizeVisible(false);
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  //   模板导入确认
  const [exportTemplateId, setExportTemplateId] = useState('');
  const [isExportTemplateVisible, setIsExportTemplateVisible] = useState(false);
  const [confirmList, setConfirmList] = useState<SwisseDeliveryEnterInfoTemplate[]>([]);
  const [confirmPageInfo, setConfirmPageInfo] = useState<Pager>(initPager);
  const getConfirmList = async () => {
    if (!isExportTemplateVisible) return;
    try {
      setLoading(true);
      const result = (await deliveryInfoEnterGetImportInfo({
        requestId: exportTemplateId || '1871455877072465921',
        ...confirmPageInfo,
      })) as IPageSwisseDeliveryEnterInfoTemplate;
      setConfirmList(result.records || []);
      setConfirmPageInfo({
        ...confirmPageInfo,
        total: Number(result.total),
      });
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (!isExportTemplateVisible) return;
    getConfirmList();
  }, [isExportTemplateVisible, confirmPageInfo.pageNum, confirmPageInfo.pageSize]);
  const confirmationTableConfig: CustomTableConfig = {
    dataSource: confirmList,
    pageInfo: confirmPageInfo,
    tableLayout: 'fixed' as 'fixed' | 'auto',
    fixedHeader: true,
    columns: [
      {
        title: '活动店铺',
        dataIndex: 'shopId',
        align: 'center',
        width: 150,
        cell: (value) => {
          return value || '';
        },
      },
      {
        title: '活动ID',
        dataIndex: 'activityId',
        align: 'center',
        width: 150,
      },
      {
        title: '活动名称',
        dataIndex: 'activityName',
        align: 'center',
        width: 150,
      },
      {
        title: '渠道',
        dataIndex: 'channel',
        align: 'center',
        width: 150,
      },
      {
        title: '奖品类型',
        dataIndex: 'prizeTypeName',
        align: 'center',
        width: 150,
      },
      {
        title: '奖品名称',
        dataIndex: 'prizeName',
        align: 'center',
        width: 150,
      },
      {
        title: '奖品数量',
        dataIndex: 'prizeNum',
        align: 'center',
        width: 150,
      },
      {
        title: '中奖时间',
        dataIndex: 'prizeTime',
        align: 'center',
        width: 150,
        cell: (value) => {
          return value && dayjs(value).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '明文pin',
        dataIndex: 'customerPin',
        align: 'center',
        width: 150,
      },
      {
        title: '昵称',
        dataIndex: 'nick',
        align: 'center',
        width: 150,
      },
      {
        title: '联系方式',
        dataIndex: 'phone',
        align: 'center',
        width: 150,
      },

      {
        title: '省',
        dataIndex: 'province',
        align: 'center',
        width: 150,
      },

      {
        title: '市',
        dataIndex: 'city',
        align: 'center',
        width: 150,
      },
      {
        title: '区',
        dataIndex: 'district',
        align: 'center',
        width: 150,
      },
      {
        title: '县',
        dataIndex: 'county',
        align: 'center',
        width: 150,
      },
      {
        title: '收货地址',
        dataIndex: 'address',
        align: 'center',
        width: 150,
      },
      {
        title: '联系人',
        dataIndex: 'receiver',
        align: 'center',
        width: 150,
      },
      {
        title: '备注',
        dataIndex: 'remark',
        align: 'center',
        width: 150,
      },
    ],
    handlePage: (p: Pager) => {
      const { pageNum, pageSize } = p;
      setConfirmPageInfo({ ...confirmPageInfo, pageNum, pageSize });
    },
  };
  const deliveryImport = async (importId: string) => {
    try {
      setLoading(true);
      await deliveryInfoEnterImport({
        importId,
      });
      LzMsg.success('上传成功');
      handleChangeTab(0);
      setIsExportTemplateVisible(false);
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  const confirmUpload = async () => {
    try {
      setLoading(true);
      const result = (await deliveryInfoEnterCheckImport({
        requestId: exportTemplateId || '1871455877072465921',
      })) as SwisseDeliveryEnterCheckImportResponse;
      // type 0 数据正常，上传 1 数据异常，继续上传2 数据异常，须修改后上传
      if (result.type === 0) {
        deliveryImport(result.importId!);
      } else if (result.type === 1) {
        Dialog.confirm({
          title: '温馨提示',
          content: `${result.errorMsg}您可以继续上传或修改后重新上传。`,
          okProps: { children: '继续上传' },
          cancelProps: { children: '放弃' },
          onOk: async () => {
            deliveryImport(result.importId!);
          },
          onCancel: () => {
            setIsExportTemplateVisible(false);
          },
        });
      } else if (result.type === 2) {
        Dialog.confirm({
          title: '温馨提示',
          content: `${result.errorMsg}`,
          footerActions: ['ok'],
          okProps: { children: '确定' },
          onOk: () => {
            setIsExportTemplateVisible(false);
          },
          onCancel: () => {
            setIsExportTemplateVisible(false);
          },
        });
      }
    } catch (e) {
      //   LzMsg.error(e.message);
      Dialog.confirm({
        title: '温馨提示',
        content: e.message,
        footerActions: ['ok'],
        okProps: { children: '确定' },
        onOk: () => {
          setIsExportTemplateVisible(false);
        },
        onCancel: () => {
          setIsExportTemplateVisible(false);
        },
      });
    } finally {
      setLoading(false);
    }
  };
  //   新建录入信息
  const importField = Field.useField();

  const recognition = () => {
    const formValues = importField.getValues();
    if (!formValues.addressInfo) return;
    const {
      name: receiver,
      phone,
      province,
      city,
      county: district,
      address,
    } = smart(formValues.addressInfo as string);
    if (province) {
      setProvinceCityDistrict(`${province} / ${city} / ${district}`);
    } else {
      setProvinceCityDistrict('');
    }
    importField.setValues({
      ...formValues,
      receiver,
      phone,
      province,
      city,
      district,
      address,
    });
  };
  /**
   * 检查对象中所有键的值是否全部为 null
   * @param {Object} obj - 需要检查的对象
   * @returns {boolean} - 如果所有值都为 null，返回 true；否则返回 false
   */
  const areAllValuesNull = (obj) => {
    if (!obj || typeof obj !== 'object') {
      throw new Error('参数必须是一个对象');
    }

    // 遍历对象的所有键
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        // 确保只遍历对象自身的属性
        if (obj[key] !== null) {
          return false; // 如果发现一个值不为 null，返回 false
        }
      }
    }

    return true; // 所有值都为 null，返回 true
  };
  const handleChange = (value, data, extra) => {
    const [productCateIdFirst, productCateIdSecond, productCateId] = extra.selectedPath.map((e) => e.label);
    setProvinceCityDistrict(`${productCateIdFirst} / ${productCateIdSecond} / ${productCateId}`);
    const formValues = importField.getValues();
    importField.setValues({
      ...formValues,
      province: productCateIdFirst,
      district: productCateId,
      city: productCateIdSecond,
    });
  };
  const addInfoSubmit = async () => {
    if (!areAllValuesNull(importField.getErrors())) {
      return;
    }

    try {
      setLoading(true);
      const formValues = importField.getValues();
      const result = (await deliveryInfoEnterCreateInfo({
        ...formValues,
        prizeTime: dayjs(formValues.prizeTime as string).format('YYYY-MM-DD HH:mm:ss'),
      })) as any;
      if (result) {
        // LzMsg.error(result);
        Dialog.confirm({
          title: '温馨提示',
          content: result,
          footerActions: ['ok'],
          okProps: { children: '确定' },
          onOk: () => {
            setIsExportTemplateVisible(false);
          },
          onCancel: () => {
            setIsExportTemplateVisible(false);
          },
        });
      } else {
        LzMsg.success('新增信息成功');
      }
      setIsAddInfoVisible(false);
      getDataList();
      getHasNewErr();
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  function debounce(func: Function, delay: number) {
    let timer: ReturnType<typeof setTimeout>;

    return function (this: any, ...args: any[]) {
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(() => {
        func.apply(this, args);
      }, delay);
    };
  }

  const [prizeNameList, setPrizeNameList] = useState<{ label: string; value: string; prizeType: number; id: string }[]>(
    [],
  );
  const handleSearchPrize = debounce(async (prizeName: string = '') => {
    const formValues = importField.getValues();
    console.log(formValues.prizeType);
    try {
      setLoading(true);
      const result = (await deliveryPrizeGetPrizeData({
        pageNum: 1,
        pageSize: 10000,
        prizeName,
      })) as IPageBdSwisseDeliveryPrize;
      const prizeNameList = result.records
        ?.map((item) => {
          return { label: item.prizeName!, value: item.prizeName!, prizeType: item.prizeType!, id: item.id! };
        })
        .filter((item) => (formValues.prizeType ? item.prizeType === formValues.prizeType : item));
      setPrizeNameList(prizeNameList || []);
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  }, 300);
  useEffect(() => {
    if (!isAddInfoVisible) return;
    handleSearchPrize();
  }, [isAddInfoVisible]);
  const checkPrizeName = (rule: any, value: string, callback: any) => {
    if (!prizeNameList.some((item) => item.value === value)) {
      callback('奖品名称不存在');
    } else {
      callback();
    }
  };
  const checkPhone = (rule: any, value: string, callback: any) => {
    if (!value) return callback();
    if (!/^(13[0-9]|14[5-9]|15[0-35-9]|166|17[0135678]|18[0-9]|19[0-35-9])\d{8}$/.test(value)) {
      callback('请输入正确的手机号');
    } else {
      callback();
    }
  };
  const checkChannel = (rule: any, value: string, callback: any) => {
    if (!channelList.some((item) => item.channel === value)) {
      callback('渠道不存在');
    } else {
      callback();
    }
  };
  const changePrizeType = (value: number) => {
    setPrizeNameList(prizeNameList.filter((item) => item.prizeType === value));
    importField.setValues({ ...importField.getValues(), prizeName: '' });
  };
  //   是否有新的异常信息
  const getHasNewErr = async () => {
    try {
      setLoading(true);
      const result = (await deliveryInfoQueryExceptionStatus()) as boolean;
      setHasNewErr(result);
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  //   重复信息
  const [isRepetitiveVisible, setIsRepetitiveVisible] = useState(false);
  const [repetitiveList, setRepetitiveList] = useState<SwisseDeliveryGetQueryInfoPageResponse[]>([]);
  const getRepetitiveList = async (infoId: string) => {
    try {
      setLoading(true);
      const result = (await deliveryInfoEnterViewRepeatInfo({
        infoId,
      })) as SwisseDeliveryGetQueryInfoPageResponse[];
      setRepetitiveList(result || []);
      setIsRepetitiveVisible(true);
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  const batchImport = () => {
    if (!selectedInfoIds.length) {
      LzMsg.error('请先选择要导入的重复信息~');
      return;
    }
    Dialog.confirm({
      title: '确认导入',
      content: `是否确认导入${selectedInfoIds.length}条重复信息？`,
      onOk: async () => {
        try {
          setLoading(true);
          await deliveryInfoEnterBatchImportRepeatInfo({
            infoIds: selectedInfoIds.join(','),
          });
          setSelectedInfoIds([]);
          getDataList();
        } catch (e) {
          LzMsg.error(e.message);
        } finally {
          setLoading(false);
        }
      },
    });
  };
  const batchDelete = async () => {
    if (!selectedInfoIds.length) {
      LzMsg.error('请先选择要删除的信息~');
      return;
    }
    Dialog.confirm({
      title: '确认删除',
      content: `是否确认删除${selectedInfoIds.length}条信息？`,
      onOk: async () => {
        try {
          setLoading(true);
          await deliveryInfoEnterBatchDelete({
            infoIds: selectedInfoIds.join(','),
          });
          setIsRepetitiveVisible(false);
          setSelectedInfoIds([]);
          getDataList();
        } catch (e) {
          LzMsg.error(e.message);
        } finally {
          setLoading(false);
        }
      },
    });
  };
  //   异常信息
  const [isEditInfo, setIsEditInfo] = useState(false);
  const editAbnormalInfo = (info: SwisseDeliveryGetQueryInfoPageResponse) => {
    importField.setValues({
      ...info,
      prizeType: prizeTypeList.find((item) => item.label === info.prizeTypeName)!.value,
    });
    setIsEditInfo(true);
    setIsAddInfoVisible(true);
  };
  const editAbnormalInfoSubmit = async () => {
    if (!areAllValuesNull(importField.getErrors())) {
      return;
    }
    try {
      setLoading(true);
      const formValues = importField.getValues();
      await deliveryInfoQueryUpdateInfo({
        ...formValues,
        shippingStatus: 0,
      });
      LzMsg.success('修改信息成功');
      setIsEditInfo(false);
      setIsAddInfoVisible(false);
      getDataList();
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  //   批量修改奖品
  const [isChangePrizeVisible, setIsChangePrizeVisible] = useState(false);
  const changePeizeField = Field.useField();
  useEffect(() => {
    if (!isChangePrizeVisible) return;
    !prizeNameList.length && handleSearchPrize();
  }, [isChangePrizeVisible]);
  const batchChangePrize = () => {
    if (!selectedInfoIds.length) {
      LzMsg.warning('请选择要修改的信息');
      return;
    }
    setIsChangePrizeVisible(true);
  };
  const currentDate = dayjs();
  // 活动主页
  return (
    <div>
      <Loading visible={loading} fullScreen>
        <LzPanel>
          <h2>信息录入</h2>
          <Box direction="row" justify="flex-start" align="center" spacing={20}>
            <Button type="primary" onClick={() => setIsAddInfoVisible(true)}>
              新增信息
            </Button>
            <Uploader
              action="/swisse/delivery/info/enter/importConfirm"
              autoUpload={true}
              withCredentials={false}
              name="upload"
              method="post"
              listType="text"
              useDataURL
              limit={1}
              onError={(res) => {
                LzMsg.error(res.message);
              }}
              onSuccess={(res) => {
                if (res.code === 200) {
                  setExportTemplateId(res.data);
                  setIsExportTemplateVisible(true);
                } else {
                  LzMsg.error(res.message);
                }
              }}
            >
              <Button className="table-cell-btn" type="secondary">
                批量上传
              </Button>
            </Uploader>
            <Button onClick={() => handleExport()}>下载模板</Button>
          </Box>
        </LzPanel>
        <LzPanel>
          <h2>信息查询</h2>

          <Form className="lz-query-criteria" field={field} colon labelAlign={'top'}>
            <FormItem label="中奖时间" name="winningTime">
              <DatePicker2.RangePicker
                hasClear={false}
                style={{ width: '100%' }}
                defaultValue={[dayjs().subtract(30, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]}
              />
            </FormItem>
            <FormItem name="shopId" label="活动店铺ID">
              <Select placeholder="请选择店铺">
                {shopList.map((item) => {
                  return (
                    <Select.Option key={item.value} value={item.value}>
                      {item.label}
                    </Select.Option>
                  );
                })}
              </Select>
            </FormItem>
            <FormItem name="activityName" label="活动名称">
              <Input trim placeholder="请输入活动名称" maxLength={20} />
            </FormItem>
            <FormItem name="pin" label="用户pin">
              <Input trim placeholder="请输入用户pin" />
            </FormItem>
            <FormItem name="phone" label="发货手机号">
              <Input trim placeholder="请输入发货手机号" maxLength={11} />
            </FormItem>
            <FormItem name="value" label="物流单号/卡密信息">
              <Input trim placeholder="请输入物流单号/卡密信息" maxLength={30} />
            </FormItem>
            <FormItem label="上传时间" name="uploadTime">
              <DatePicker2.RangePicker
                hasClear={false}
                style={{ width: '100%' }}
                defaultValue={[dayjs().subtract(30, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]}
              />
            </FormItem>
            <Form.Item label="是否需要充值卡密" name="keyStatus">
              <RadioGroup>
                <Radio value={1}>是</Radio>
                <Radio value={0}>否</Radio>
              </RadioGroup>
            </Form.Item>
            <FormItem name="channel" label="录入渠道">
              <Select>
                {channelList.map((item) => {
                  return (
                    <Select.Option key={item.id} value={item.channel}>
                      {item.channel}{' '}
                    </Select.Option>
                  );
                })}
              </Select>
            </FormItem>
            <FormItem name="prizeName" label="奖品名称">
              <Input trim placeholder="请输入奖品名称" />
            </FormItem>
            {infoStatus === 2 && (
              <FormItem label="异常原因" name="remark">
                <Input trim placeholder="请输入异常原因" maxLength={50} />
              </FormItem>
            )}
            <FormItem colon={false}>
              <Form.Reset style={{ marginRight: '10px' }}>重置</Form.Reset>
              <Form.Submit validate type="primary" style={{ marginRight: '10px' }} onClick={handleSubmit}>
                查询
              </Form.Submit>
              {/* <Button type="primary">导出</Button> */}
            </FormItem>
          </Form>
          <Tab activeKey={`${infoStatus}`}>
            <Tab.Item title="已录入" onClick={() => handleChangeTab(0)}>
              <CustomTable customTableConfig={customTableConfig}></CustomTable>
            </Tab.Item>
            <Tab.Item title="重复信息" onClick={() => handleChangeTab(1)}>
              <Message type="notice">
                以下导入信息与已有信息存在一致，可能会造成奖品超发等情况，是否为您删除？
                （信息一致指：用户pin、奖品名称、中奖时间*只筛选至日期，不精确分秒均一致）
              </Message>
              <Box margin={[10, 0]} direction="row" justify="flex-start" align="center" spacing={20}>
                <Button type="primary" onClick={() => batchImport()}>
                  批量导入
                </Button>
                <Button type="primary" onClick={() => batchDelete()}>
                  批量删除
                </Button>
              </Box>
              <CustomTable customTableConfig={RepetitiveTableConfig}></CustomTable>
            </Tab.Item>
            <Tab.Item title={<Badge dot={hasNewErr}>异常</Badge>} onClick={() => handleChangeTab(2)}>
              <Message type="notice">以下存在奖品编码不匹配等异常状态，请手动处理完成后进行导入</Message>
              <Box direction="row" margin={[10, 0]} justify="flex-start" align="center" spacing={20}>
                <Button type="primary" onClick={() => batchChangePrize()}>
                  批量修改奖品
                </Button>
                <Button type="primary" onClick={() => batchDelete()}>
                  批量删除
                </Button>
              </Box>
              <CustomTable customTableConfig={abnormalTableConfig}></CustomTable>
            </Tab.Item>
          </Tab>
        </LzPanel>
      </Loading>
      <Dialog
        v2
        title="选择模板奖品"
        width={1200}
        visible={isSelectPrizeVisible}
        footer={false}
        onClose={() => {
          setPrizePageInfo(initPager);
          setSelectedprizesList([]);
          setTableSelectedRecords([]);
          setSearchInfo({});
          setIsSelectPrizeVisible(false);
        }}
      >
        <Row>
          <Col span="11">
            <Form field={prizeField} labelCol={{ span: 5 }} fullWidth useLabelForErrorMessage>
              <FormItem label="奖品类型">
                <Select name="prizeType" placeholder="请选择奖品类型">
                  {prizeTypeList.map((item) => {
                    return (
                      <Select.Option key={item.value} value={item.value}>
                        {item.label}
                      </Select.Option>
                    );
                  })}
                </Select>
              </FormItem>
              <FormItem label="奖品编码">
                <Input trim name="physicalCode" placeholder="请输入奖品编码" />
              </FormItem>
              <FormItem label="奖品名称">
                <Input trim name="prizeName" placeholder="请输入奖品名称" />
              </FormItem>
              <Box direction="row" justify="flex-end">
                <Form.Submit type="primary" onClick={queryPrize}>
                  查询
                </Form.Submit>
              </Box>
            </Form>
          </Col>
          <Col span="11" offset={2}>
            <Message type="notice">已选择奖品（可点击删除）</Message>
            <div style={{ marginTop: '10px', maxHeight: '150px', overflowY: 'auto' }}>
              {selectedprizesList.map((item: BdSwisseDeliveryPrize) => (
                <Tag
                  key={item.id}
                  closable
                  style={{ margin: '5px' }}
                  onClose={() => {
                    setSelectedprizesList(selectedprizesList.filter((i) => i.id !== item.id));
                  }}
                >
                  {item.prizeName}
                </Tag>
              ))}
            </div>
          </Col>
        </Row>
        <Box style={{ marginTop: '20px' }} direction="row" justify="space-between" align="center" wrap>
          <Button
            type="primary"
            onClick={() => {
              setSelectedprizesList([...new Set([...selectedprizesList, ...tableSelectedRecords])]);
            }}
          >
            添加勾选奖品到已选择
          </Button>
          <Button type="primary" onClick={exportTemplateByPrizes}>
            根据已选择奖品导出模板
          </Button>
        </Box>

        <CustomTable customTableConfig={prizeDialogTableConfig}></CustomTable>
      </Dialog>
      <Dialog
        v2
        title="确认导入信息"
        width={1200}
        visible={isExportTemplateVisible}
        onClose={() => {
          setIsExportTemplateVisible(false);
        }}
        okProps={{ children: '确认上传' }}
        cancelProps={{ children: '放弃' }}
        onOk={confirmUpload}
      >
        <CustomTable customTableConfig={confirmationTableConfig}></CustomTable>
      </Dialog>
      {/* 新建录入信息 */}
      <Dialog
        v2
        title="录入信息"
        width={800}
        visible={isAddInfoVisible}
        footer={false}
        onClose={() => setIsAddInfoVisible(false)}
      >
        <Form field={importField} labelCol={{ span: 5 }} fullWidth useLabelForErrorMessage>
          <FormItem label="店铺ID" name="shopId">
            <Select placeholder="请选择店铺" required>
              {shopList.map((item) => {
                return (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          </FormItem>
          <Form.Item label="渠道" required name="channel" validator={checkChannel.bind(this)}>
            <Select
              showSearch
              filterLocal={false}
              dataSource={channelList}
              onSearch={getChanelList}
              placeholder="请选择渠道"
            >
              {channelList.map((item) => {
                return (
                  <Select.Option key={item.id} value={item.channel}>
                    {item.channel}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          <FormItem label="活动名称" name="activityName" required>
            <Input trim placeholder="请输入活动名称" maxLength={30} />
          </FormItem>
          <FormItem label="活动ID" name="activityId">
            <Input trim placeholder="请输入活动ID" maxLength={64} />
          </FormItem>
          <Form.Item label="用户昵称" name="nick">
            <Input trim placeholder="请输入用户昵称" maxLength={30} />
          </Form.Item>
          <Form.Item label="用户pin" required name="customerPin">
            <Input trim placeholder="请输入用户pin" maxLength={100} />
          </Form.Item>
          <Form.Item label="奖品类型" required name="prizeType">
            <Select placeholder="请选择奖品类型" onChange={changePrizeType}>
              {prizeTypeList.map((item) => {
                return (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item label="奖品名称" required name="prizeName" validator={checkPrizeName.bind(this)}>
            <Select
              showSearch
              filterLocal={false}
              dataSource={prizeNameList}
              onSearch={handleSearchPrize}
              placeholder="请选择奖品名称"
            >
              {prizeNameList.map((item) => {
                return (
                  <Select.Option key={item.label} value={item.value}>
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item label="奖品数量" required name="prizeNum">
            <NumberPicker
              style={{ width: '100%' }}
              placeholder="请输入奖品数量"
              defaultValue={1}
              step={1}
              min={1}
              max={9999999999}
            />
          </Form.Item>
          <Form.Item label="联系人" name="receiver">
            <Input trim placeholder="请输入联系人" maxLength={20} />
          </Form.Item>
          <Form.Item label="联系方式" name="phone" validator={checkPhone.bind(this)}>
            <Input trim placeholder="请输入联系方式" maxLength={11} />
          </Form.Item>
          {/* <Form.Item className="item" label="dizhi ">
            <CascaderSelect value={treeValue} dataSource={areaList} onChange={handleChange} />
          </Form.Item> */}
          {importField.getValues().prizeType === 1 && (
            <>
              <Form.Item label="省/市/区" name="provinceCityDistrict">
                {/* <Input trim placeholder="请输入省" /> */}
                <CascaderSelect
                  value={provinceCityDistrict}
                  placeholder="请选择省/市/区"
                  dataSource={areaList}
                  onChange={handleChange}
                />
              </Form.Item>
              {/* <Form.Item label="市" name="city">
                <Input trim placeholder="请输入市" />
              </Form.Item>
              <Form.Item label="区" name="district">
                <Input trim placeholder="请输入区" />
              </Form.Item> */}
              {/* <Form.Item label="县" name="county">
                <Input trim placeholder="请输入县" />
              </Form.Item> */}
              <Form.Item label="收货地址" name="address">
                <Input.TextArea composition placeholder="收货地址" />
              </Form.Item>
              <Form.Item label={addressInfoLabel} name="addressInfo">
                <Input.TextArea placeholder="请输入收货地址" composition />
                <Button onClick={() => recognition()}>智能识别</Button>
              </Form.Item>
            </>
          )}
          <Form.Item label="中奖时间" required name="prizeTime">
            <DatePicker2
              showTime
              disabledDate={(date) => {
                return date > currentDate;
              }}
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item>
          <Form.Item label="备注" name="remark">
            <Input.TextArea placeholder="请输入备注" />
          </Form.Item>
          <Box direction="row" align="center" justify="flex-end" style={{ marginTop: '10px' }} spacing={20}>
            <Form.Submit
              validate
              type="primary"
              onClick={() => (isEditInfo ? editAbnormalInfoSubmit() : addInfoSubmit())}
            >
              确定
            </Form.Submit>
            <Form.Submit onClick={() => setIsAddInfoVisible(false)}>取消</Form.Submit>
          </Box>
        </Form>
      </Dialog>
      {/* 重复信息展示 */}
      <Dialog
        v2
        title="重复信息"
        width={1200}
        visible={isRepetitiveVisible}
        onOk={() => {
          setIsRepetitiveVisible(false);
        }}
        onClose={() => {
          setIsRepetitiveVisible(false);
        }}
      >
        <CustomTable
          customTableConfig={{
            ...customTableConfig,
            dataSource: repetitiveList,
            emptyContent: '原重复信息已修改，当前无重复数据，可处理导入',
            hasPagination: false,
          }}
        ></CustomTable>
      </Dialog>
      <Dialog
        v2
        title="批量修改奖品"
        width={400}
        visible={isChangePrizeVisible}
        onOk={() => {
          changePeizeField.validate(async (errors) => {
            if (errors) return;
            const formValues = changePeizeField.getValues();
            Dialog.confirm({
              title: '确认修改奖品',
              content: `确认将所选${selectedInfoIds.length}条信息的奖品修改为${formValues.prizeName}？`,
              onOk: async () => {
                try {
                  setLoading(true);
                  await deliveryInfoErrorInfoBatchUpdatePrize({
                    infoIds: selectedInfoIds,
                    prizeId: prizeNameList.find((item) => item.label === formValues.prizeName)!.id,
                  });
                  LzMsg.success('修改奖品成功');
                  setIsChangePrizeVisible(false);
                  setSelectedInfoIds([]);
                  getDataList();
                } catch (e) {
                  LzMsg.error(e.message);
                } finally {
                  setLoading(false);
                }
              },
            });
          });
        }}
        onClose={() => setIsChangePrizeVisible(false)}
      >
        <Form field={changePeizeField} labelCol={{ span: 5 }} fullWidth useLabelForErrorMessage>
          <Form.Item label="奖品名称" required name="prizeName" validator={checkPrizeName.bind(this)}>
            <Select
              showSearch
              filterLocal={false}
              dataSource={prizeNameList}
              onSearch={handleSearchPrize}
              placeholder="请选择奖品名称"
            >
              {prizeNameList.map((item) => {
                return (
                  <Select.Option key={item.label} value={item.value}>
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
        </Form>
      </Dialog>
    </div>
  );
};
