import React, { useEffect, useReducer, useRef, useState } from "react";
import {
  <PERSON>oon,
  Box,
  Button,
  Dialog,
  Drawer,
  Form,
  Icon,
  Input,
  Loading,
  Message,
  Tab,
  Table,
  Typography,
} from '@alifd/next';
import { RouteComponentProps } from 'react-router-dom';
import { deleteActivity, endActivity, getActivityInfoAllOrigin, getActivityType } from '@/api/common';
import { getActivityPageListDz } from '@/api/customized';
import {
  CommonActivityPageResponse,
  GetActivityAllOriginResponse,
  GetActivitySkuResp,
  IPageCommonActivityPageResponse,
  SyncActivitySkuResp,
} from '@/api/types';
import SearchForm from './components/SearchForm';
import LzPanel from '@/components/LzPanel';
import dayjs from 'dayjs';
import constants from '@/utils/constant';
import relativeTime from 'dayjs/plugin/relativeTime';
import LzButtonGroup from '@/components/LzButtonGroup';
import LzQRCode from '@/components/LzQRCode';
import { appHistory } from '@ice/stark-app';
import LzPagination, { Pager } from '@/components/LzPagination';
import QS from 'query-string';
import lodash from 'lodash';
import styles from './style.module.scss';
import { useStateWithStorage } from '@/hooks/useStateWithStorage';
import { config } from 'ice';
import 'dayjs/locale/zh-cn';
import { deleteDraft } from '@/api/draft';
import LzEmpty from '@/components/LzEmpty';
import { getActivitySkuList, updateActivitySkuList } from '@/api/sku';
import LzDialog from '@/components/LzDialog';
import Utils, { deepCopy, getParams, removeOlderInteractSessionStorage } from '@/utils';
import format from '@/utils/format';
import { getShop } from '@/utils/shopUtil';
import PropertyInfo from '@/pages/core/list/components/PropertyInfo';
import store from '@/store';
import { initData } from '@/models/customizedPager';

dayjs.extend(relativeTime);
dayjs.locale('zh-cn');
const initPager: Pager = {
  pageNum: 1,
  pageSize: 20,
  total: 0,
};

export default (props: RouteComponentProps) => {
  const search = QS.parse(props.location.search);
  const { history } = props;
  const [_, bsdPagerAction] = store.useModel('bsdPager');

  const bsdPager = JSON.parse(getParams('params')) || initData;

  const [activeKey, setActiveKey] = useStateWithStorage<string>(bsdPager.activeKey || '2');
  const [skuIdValue, setSkuIdValue] = useState('');
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [params, setParams] = useReducer((prevState, currState) => ({ ...prevState, ...currState }), {
    ...initPager,
    ...search,
    status: activeKey,
    ...bsdPager,
  });
  // 活动列表
  const [list, setList] = useState<CommonActivityPageResponse[]>([]);
  // 页面loading
  const [loading, setLoading] = useState(false);
  // 图片抽屉loading
  const [imgLoading, setImgLoading] = useState(false);
  // 是否展示商品抽屉
  const [syncVisible, setSyncVisible] = useState(false);
  // 是否展示详情抽屉
  const [drawerVisible, setDrawerVisible] = useState(false);
  // 详情抽屉tabIndex
  const [drawerTabIndex, setDrawerTabIndex] = useState('1');
  // 分页数据
  const [pageInfo, setPageInfo] = useState<Pager>(initPager);
  // 获取v2活动的原始数据
  const [activityInfo, setActivityInfo] = useState<CommonActivityPageResponse>({});
  const [decoInfo, setDecoInfo] = useState<any>({});
  // 店铺ID
  const { shopId } = getShop();
  // 详情抽屉中的c端dom
  const iframeRef = useRef<HTMLIFrameElement | null>(null);
  // 商品列表
  const [skuList, setSkuList] = useState<GetActivitySkuResp[]>([]);
  // 初始化的商品列表，用户前端筛选重置
  const [initSkuList, setInitSkuList] = useState<GetActivitySkuResp[]>([]);
  // 当前操作的活动信息
  const [targetActivity, setTargetActivity] = useState<CommonActivityPageResponse>({});
  // 同步后的商品信息
  const [syncedSku, setSyncedSku] = useState<GetActivitySkuResp>({});
  // 是否展示同步结果弹窗
  const [synced, setSynced] = useState(false);
  // 当前同步的商品信息
  const [targetSku, setTargetSku] = useState<GetActivitySkuResp>({});
  const [targetRow, setTargetRow] = useState(null);
  const { venderType } = JSON.parse(localStorage.getItem('LZ_CURRENT_SHOP') || '{}');
  // 已经迁移至v2的互动类型
  const [type, setType] = React.useState<number[]>([]);

  const [targetVersion, setTargetVersion] = React.useState('v3');

  // 格式化日期
  const useDateRangeCell = (value, index, record) => {
    if (record.startTime && record.endTime) {
      return (
        <div>
          <div>起：{dayjs(record.startTime).format(constants.DATE_FORMAT_TEMPLATE)}</div>
          <div>止：{dayjs(record.endTime).format(constants.DATE_FORMAT_TEMPLATE)}</div>
        </div>
      );
    } else {
      return <div>-</div>;
    }
  };
  // 创建时间行
  const useDateFormatCell = (value, index, record) => {
    // 给date1 date2起名
    const currentTimeToStartTime = new Date().getTime() - new Date(record.startTime).getTime();
    const durationStartTimeToEnd = new Date(record.endTime).getTime() - new Date(record.startTime).getTime();
    let progress = (currentTimeToStartTime / durationStartTimeToEnd) * 100;
    progress < 1 && (progress = 1);
    progress > 100 && (progress = 100);
    return (
      <div
        className={styles.timeCell}
        style={
          {
            '--progress-length': `${progress}%`,
            '--color': activeKey === '2' ? '#1677FF' : '#FFFFFF',
          } as any
        }
      >
        <div>{record.createTime ? dayjs(record.createTime).format(constants.DATE_FORMAT_TEMPLATE) : '-'}</div>
        {activeKey === '2' && <div>{`已开始 ${dayjs(record.startTime).fromNow(true)}`}</div>}
        {activeKey === '1' && <div>{`距离开始 ${dayjs(record.startTime).fromNow(true)}`}</div>}
      </div>
    );
  };
  // 获取活动列表
  const loadData = async () => {
    setLoading(true);
    !params.status && (params.status = '2');
    try {
      const newParams = {
        status: params.status ?? '',
        activityName: params.activityName ?? '',
        activityType: params.activityType ?? [],
        dateRange: params.dateRange ?? [],
        activeKey: params.activeKey ?? '2',
      };
      const result: IPageCommonActivityPageResponse = await getActivityPageListDz({ ...newParams, ...bsdPager });
      setList(result.records || []);
      setPageInfo({ total: result.total, pageNum: result.current, pageSize: result.size } as any);
    } finally {
      setLoading(false);
    }
  };
  // 搜索

  const handleSearch = (formValue) => {
    formValue.pageNum = 1;
    setParams(formValue);
  };

  useEffect(() => {
    loadData().then();
  }, [params]);

  const onRowChange = (selectedKeys) => {
    setSelectedRows(selectedKeys);
  };

  const rowSelection = {
    selectedRowKeys: selectedRows,
    onChange: onRowChange,
  };
  // 列表tab改变
  const handleTabChange = (tabIndex): boolean | void => {
    setActiveKey(tabIndex);
    bsdPagerAction.setState({
      activeKey: tabIndex,
    });
    setPageInfo(initPager);
    setList([]);
    setParams({ status: tabIndex, ...initPager });
    onRowChange([]);
  };

  // 根据版本使用不同的router
  const autoPush = (link: string, version) => {
    switch (version) {
      case 'v1': {
        appHistory.push(link);
        break;
      }
      case 'v2': {
        appHistory.push(`/interact2${link}`);
        break
      }
      case 'v3': {
        history.push(link);
        break
      }
      default: {
        history.push(link);
      }
    }
  };

  // 停止活动
  const stopActivity = (activityIdList: any[]) => {
    Dialog.confirm({
      v2: true,
      title: '提示',
      centered: true,
      content: '是否确认停止活动?',
      onOk: async (): Promise<void> => {
        setLoading(true);
        endActivity({
          activityIdList,
        }).then((): void => {
          Message.success('操作成功');
          setSelectedRows([]);
          loadData().then();
          setLoading(false);
        });
      },
    } as any);
  };

  // 删除活动
  const removeActivity = (activityIdList: any[]) => {
    Dialog.confirm({
      v2: true,
      title: '提示',
      centered: true,
      content: '是否确认删除该活动?',
      onOk: async (): Promise<void> => {
        deleteActivity({ activityIdList }).then((): void => {
          Message.success('操作成功');
          setSelectedRows([]);
          loadData().then();
        });
      },
    } as any);
  };
  // 删除草稿
  const removeDraft = (activityIdList: any[]) => {
    Dialog.confirm({
      v2: true,
      title: '提示',
      centered: true,
      content: '是否确认删除选中草稿?',
      onOk: async (): Promise<void> => {
        setLoading(true);
        deleteDraft({ activityIdList }).then((): void => {
          Message.success('操作成功');
          setLoading(false);
          loadData().then();
        });
      },
    } as any);
  };

  // 获取活动中使用的商品列表
  const getSkuImgList = (record) => {
    setSyncVisible(true);
    setImgLoading(true);
    setTargetActivity(record);
    getActivitySkuList({
      activityId: record.id,
      module: record.module,
    }).then((res: GetActivitySkuResp[]) => {
      let result = deepCopy(res);
      if (skuIdValue) {
        result = result.filter((e) => e.skuId.toString() === skuIdValue.trim());
        console.log(result);
      }
      setSkuList(result);
      setInitSkuList(res);
      setImgLoading(false);
    });
  };
  // 按钮事件列表
  const events = {
    // 查看活动按钮点击事件
    onViewButtonClick: (value, index, record) => {
      console.log('onViewButtonClick', value, index, record);
      // 重置版本
      setTargetVersion('');
      setDrawerTabIndex(record.module === 'v3' ? '1': '2');
      if (record.module === 'v2' || record.module === 'v3') {
        setLoading(true);
        getActivityInfoAllOrigin({ id: record.id, type: 'view' })
          .then((res: GetActivityAllOriginResponse) => {
            const activityData = JSON.parse(res.activityData!);
            const decoData = JSON.parse(res.decoData!);
            // 字段都不相同，避免麻烦合并算了
            setActivityInfo({ ...record, ...activityData });
            setDecoInfo(decoData);
            setDrawerVisible(true);
            setLoading(false);
            // 兼容V2 V3 预览
            setTargetVersion(record.module);
          })
          .catch(() => {
            setLoading(false);
          });
      } else {
        const viewLink = record.buttons.filter((e) => e.key === 'view')[0].link;
        appHistory.push(viewLink);
      }
    },
    // 编辑活动按钮点击事件
    onEditButtonClick: (value, index, record) => {
      console.log('onEditButtonClick', value, index, record);
      const { module } = record;
      const { link } = record.buttons.filter((e) => e.key === 'edit')[0];
      autoPush(link, module)
    },
    // 继续编辑
    onContinueButtonClick: (value, index, record) => {
      console.log('onContinueButtonClick', value, index, record);
      const { module } = record;
      const { link } = record.buttons.filter((e) => e.key === 'continue')[0];
      autoPush(link, module)
    },
    // 复制活动按钮点击事件
    onCopyButtonClick: (value, index, record) => {
      console.log('onCopyButtonClick', value, index, record);
      if (type.includes(record.activityType) && record.module === 'v1') {
        Dialog.confirm({
          v2: true,
          title: '温馨提示',
          centered: true,
          content: '该活动已进行升级，点击确认前往创建新活动',
          onOk: async (): Promise<void> => {
            history.push(`/activity/select?activityType=${record.activityType}`);
          },
        } as any);
      } else {
        const { module } = record;
        const { link } = record.buttons.filter((e) => e.key === 'copy')[0];
        autoPush(link, module)
      }
    },
    // 活动记录按钮点击事件
    onRecordButtonClick: (value, index, record) => {
      console.log('onRecordButtonClick', value, index, record);
      const { module } = record;
      const { link } = record.buttons.filter((e) => e.key === 'record')[0];
      autoPush(link, module)
    },
    // 活动数据按钮点击事件
    onDataButtonClick: (value, index, record) => {
      console.log('onDataButtonClick', value, index, record);
      const { module } = record;
      const { link } = record.buttons.filter((e) => e.key === 'data')[0];
      autoPush(link, module)
    },
    // 活动分析按钮点击事件
    onAnalysisButtonClick: (value, index, record) => {
      console.log('onAnalysisButtonClick', value, index, record);
      const { module } = record;
      const { link } = record.buttons.filter((e) => e.key === 'analysis')[0];
      autoPush(link, module)
    },
    // 同步活动图片按钮点击事件
    onSyncPicButtonClick: (value, index, record) => {
      console.log('onSyncPicButtonClick', value, index, record);
    },
    // 停止活动按钮点击事件
    onStopButtonClick: (value, index, record) => {
      console.log('onStopButtonClick', value, index, record);
      stopActivity([
        {
          id: record.id,
          module: record.module,
        },
      ]);
    },
    // 删除活动按钮点击事件
    onDeleteButtonClick: (value, index, record) => {
      console.log('onDeleteButtonClick', value, index, record);
      removeActivity([
        {
          id: record.id,
          module: record.module,
        },
      ]);
    },
    // 删除草稿按钮点击事件
    onThrowButtonClick: (value, index, record) => {
      console.log('onThrowButtonClick', value, index, record);
      removeDraft([
        {
          id: record.id,
          module: record.module,
        },
      ]);
    },
    // 同步图片按钮点击事件
    onSyncButtonClick: (value, index, record) => {
      console.log('onSyncButtonClick', value, index, record);
      setTargetRow(record);
      getSkuImgList(record);
    },
    // 跳转页面按钮点击事件
    onJumpButtonClick: (value, index, record) => {
      console.log('onJumpButtonClick', value, index, record);
      const { link } = record.buttons.filter((e) => e.key === 'jump')[0];
      appHistory.push(
        `${link}?id=${record.id}&startTime=${format.formatDateTimeDayjs(
          record.startTime,
        )}&endTime=${format.formatDateTimeDayjs(record.endTime)}&name=${encodeURIComponent(record.activityName)}&type=${
          record.activityType
        }`,
      );
    },
  };

  /**
   * @description 操作列
   * @param value
   * @param index
   * @param record
   */
  const operateCell = (value, index, record) => {
    const onButtonClick = (button) => {
      // console.log('按钮操作', button);
      const eventName = `on${lodash.upperFirst(button.key)}ButtonClick`;
      // console.log('按钮操作11111111', eventName);
      if (Reflect.has(events, eventName)) {
        // console.log('按钮操作2222222222', events[eventName]);
        Reflect.apply(events[eventName], null, [value, index, record]);
      } else {
        console.error(`method ${eventName} not found`);
      }
    };
    return <LzButtonGroup buttons={record.buttons} onClick={onButtonClick} />;
  };
  // 活动名称列
  const titleCell = (value, index, record) => {
    return (
      <Box spacing={10} direction="row" align={'center'}>
        {activeKey !== '4' && !record.activityType.toString().startsWith('5') && (
          <Balloon
            autoFocus={false}
            closable={false}
            triggerType="click"
            followTrigger
            trigger={
              <Button type={'secondary'} text>
                <i className="iconfont icon-icon5-18" style={{ fontSize: 22, lineHeight: 1 }} />
              </Button>
            }
            align="r"
          >
            <LzQRCode longUrl={record.longUrl} shortUrl={record.shortUrl} qrcodeUrlTitle={value} />
          </Balloon>
        )}
        <div>
          <Typography.Text strong className="nowrap ellipsis" style={{ display: 'block' }}>
            {value}
          </Typography.Text>
          <div className={styles.inARow}>
            <div className="nowrap text-gray">{record.id}</div>
            <span
              className={`iconfont icon-fuzhi ${styles.copy}`}
              onClick={() => {
                copyText(record.id).then();
              }}
            />
          </div>
        </div>
      </Box>
    );
  };
  const copyText = async (link: string) => {
    await Utils.copyText(link);
    Message.success('活动id已经复制到剪切板');
  };
  /**
   * 异步导入各活动预览组建
   * @param data 活动数据
   * @return JSX｜null
   */
  const AsyncComponents = ({ data }): React.ReactElement | null => {
    console.log(`@/pages/activity/${data.activityType}/${data.templateCode}/preview`);
    try {
      const dynamicLoadComponent = () => {
        return require(`@/pages/activity/${data.activityType}/${data.templateCode}/preview`).default;
      };
      const DynamicComponent = dynamicLoadComponent();
      return (
        <div>
          <DynamicComponent defaultValue={data} labelAlign="top" value={data} />
        </div>
      );
    } catch (e) {
      console.error('v1版本活动，未匹配到组件');
      return <LzEmpty />;
    }
  };
  /**
   * 向c端发送消息
   */
  const sendMessage = (formData, sendType: string): void => {
    const iWindow = iframeRef.current?.contentWindow;
    console.log(`📧📧📧列表向c端发送消息  ${new Date()}`, sendType);
    console.log(formData);
    iWindow?.postMessage(
      {
        from: 'B',
        type: sendType,
        event: 'update',
        data: formData,
      },
      '*',
    );
  };
  // 同步图片
  const syncImage = (sku: GetActivitySkuResp): void => {
    setImgLoading(true);
    setTargetSku(sku);
    updateActivitySkuList({
      activityId: targetActivity.id,
      skuIds: [sku.skuId as string],
      module: targetActivity.module,
    })
      .then((res: SyncActivitySkuResp[]): void | boolean => {
        const updatedSku: SyncActivitySkuResp = res[0];
        updatedSku.price = sku.price;
        const sameIndex: number = skuList.findIndex((item: GetActivitySkuResp): boolean => item.skuId === sku.skuId);
        if (updatedSku) {
          skuList.splice(sameIndex, 1, res[0]);
          setSkuList(skuList);
          setSyncedSku(res[0]);
          setSynced(true);
          Message.success('同步图片成功！');
        } else {
          Message.error('同步图片失败！');
          return false;
        }
      })
      .catch((e): void => {
        Message.error(e.message);
      })
      .finally((): void => {
        setImgLoading(false);
      });
  };


  useEffect((): void => {
    if (iframeRef.current) {
      iframeRef.current.onload = (): void => {
        console.log('🚀🚀🚀列表iframe加载完成...');
        sendMessage(decoInfo, 'deco');
        sendMessage(activityInfo, 'activity');
        const { shopName } = getShop();
        sendMessage(shopName, 'shop');
      };
    }
  }, [drawerTabIndex, targetVersion]);

  const getType = async () => {
    const res = await getActivityType();
    setType(res);
  };
  useEffect(() => {
    getType();
  }, []);

  useEffect((): void => {
    removeOlderInteractSessionStorage();
  });
  return (
    <div className={styles.activityList}>
      {/* <div className={styles.toOld}> */}
      {/*  <Button text type="primary" onClick={() => appHistory.push('/interaction/activityManage')}> */}
      {/*    回到旧版 */}
      {/*  </Button> */}
      {/* </div> */}
      <Loading visible={loading} style={{ width: '100%' }}>
        <Tab activeKey={activeKey} defaultActiveKey="2" onChange={handleTabChange}>
          <Tab.Item title="未开始活动" key="1" />
          <Tab.Item title="进行中活动" key="2" />
          <Tab.Item title="已结束活动" key="3" />
          <Tab.Item title="草稿箱" key="4" />
        </Tab>
        <LzPanel>
          <SearchForm onSearch={handleSearch} />
        </LzPanel>
        <LzPanel>
          <Box direction="row" justify="space-between" spacing={20}>
            <Box direction="row" spacing={10}>
              {(activeKey === '2' || activeKey === '1') && (
                <Button
                  type="normal"
                  warning
                  disabled={selectedRows.length === 0}
                  onClick={() => {
                    const selectedList = list.filter((e) => selectedRows.includes(e.id));
                    const param = selectedList.map((e) => {
                      return {
                        id: e.id,
                        module: e.module,
                      };
                    });
                    stopActivity(param);
                  }}
                >
                  批量结束活动
                </Button>
              )}
              {activeKey === '3' && (
                <Button
                  type="normal"
                  warning
                  disabled={selectedRows.length === 0}
                  onClick={() => {
                    const selectedList = list.filter((e) => selectedRows.includes(e.id));
                    const param = selectedList.map((e) => {
                      return {
                        id: e.id,
                        module: e.module,
                      };
                    });
                    removeActivity(param);
                  }}
                >
                  批量删除活动
                </Button>
              )}
              {activeKey === '4' && (
                <Button
                  type="normal"
                  warning
                  disabled={selectedRows.length === 0}
                  onClick={() => {
                    const selectedList = list.filter((e) => selectedRows.includes(e.id));
                    const param = selectedList.map((e) => {
                      return {
                        id: e.id,
                        module: e.module,
                      };
                    });
                    removeDraft(param);
                  }}
                >
                  批量删除草稿
                </Button>
              )}
            </Box>
            {activeKey !== '4' && (
              <Button
                type="secondary"
                style={{ justifySelf: 'flex-end' }}
                onClick={() => appHistory.push('/property/product')}
              >
                实物发货管理 <Icon type="arrow-right" />
              </Button>
            )}
          </Box>
          <br />
          <Table dataSource={list} rowSelection={rowSelection} className="lz-table">
            <Table.Column
              title="缩略图"
              width={70}
              align={'center'}
              cell={(value, index, record) => (
                <Balloon
                  autoFocus={false}
                  closable={false}
                  triggerType="hover"
                  followTrigger
                  trigger={
                    record.templateCover ? (
                      <img style={{ height: 50, cursor: 'pointer' }} src={record.templateCover} alt="" />
                    ) : (
                      <div style={{ height: 52 }} />
                    )
                  }
                  align="r"
                >
                  <img src={record.templateCover} style={{ height: 300 }} alt="" />
                </Balloon>
              )}
            />
            <Table.Column title="活动名称" dataIndex="activityName" cell={titleCell} wordBreak={'word'} />
            <Table.Column
              title="活动类型"
              dataIndex="activityType"
              cell={(value, index, record) => record.activityTypeName}
            />
            <Table.Column title="有效期" dataIndex="startTime" cell={useDateRangeCell} />
            <Table.Column title="创建时间" dataIndex="createTime" cell={useDateFormatCell} />
            <Table.Column title="操作" dataIndex="id" cell={operateCell} width={200} />
          </Table>
          <LzPagination
            total={pageInfo.total}
            pageNum={pageInfo.pageNum}
            pageSize={pageInfo.pageSize}
            onChange={(obj: Pager) => {
              setParams(obj);
              bsdPagerAction.setState({
                ...obj,
              });
            }}
          />
        </LzPanel>
        {/* v2详情 */}
        <Drawer
          title={
            <Tab
              navStyle={{ margin: '-13px -15px', width: '90%', border: 'none' }}
              onChange={setDrawerTabIndex}
              activeKey={drawerTabIndex}
            >
              {targetVersion === 'v3' && <Tab.Item title="活动详情" key="1" />}
              <Tab.Item title="活动预览" key="2" />
              <Tab.Item title="资产详情" key="3" />
            </Tab>
          }
          placement="right"
          visible={drawerVisible}
          width={750}
          bodyStyle={{
            height: 'calc(100% - 42px)',
            overflow: 'auto',
          }}
          closeMode={['close', 'esc', 'mask']}
          onClose={() => setDrawerVisible(false)}
        >
          {drawerTabIndex === '1' && <AsyncComponents data={activityInfo} />}
          {drawerTabIndex === '2' && (
            <div style={{ textAlign: 'center', height: '100%' }}>
              <iframe
                ref={iframeRef}
                style={{ width: '375px', height: '100vh' }}
                src={`${targetVersion === 'v3'
                  ? config.previewUrl
                  : config.v2PreviewUrl}/${activityInfo.activityType}/${activityInfo.templateCode}/preview/?activityId=${activityInfo.id}&shopId=${shopId}&venderType=${venderType}`}
                className={styles.previewIframe}
              />
            </div>
          )}
          {drawerTabIndex === '3' && <PropertyInfo id={activityInfo.id} />}
        </Drawer>
      </Loading>
      {/*  商品列表同步图片 */}
      <Drawer
        title="商品列表"
        placement="right"
        visible={syncVisible}
        width={600}
        bodyStyle={{
          height: 'calc(100% - 50px)',
          overflow: 'auto',
        }}
        closeMode={['close', 'esc', 'mask']}
        onClose={() => {
          setSkuIdValue('');
          setSyncVisible(false);
        }}
      >
        <Loading visible={imgLoading} style={{ width: '100%' }}>
          <Form labelAlign="left" inline>
            <Form.Item label="SKUID">
              <Input placeholder="请输入SKUID" name="skuId" value={skuIdValue} onChange={(val: string) => setSkuIdValue(val)} />
            </Form.Item>
            <Form.Item label=" " colon={false}>
              <Form.Submit
                type="primary"
                validate
                onClick={(value: any) => {
                  if (value.skuId) {
                    const result: GetActivitySkuResp[] = skuList.filter(
                      (sku: GetActivitySkuResp): boolean => sku.skuId!.toString() === value.skuId.trim(),
                    );
                    setSkuList(result);
                  } else {
                    setSkuList(initSkuList);
                  }
                }}
                style={{ marginRight: 8 }}
              >
                查询
              </Form.Submit>
              <Form.Reset
                onClick={() => {
                  setSkuIdValue('');
                  setSkuList(initSkuList);
                }}
              >
                重置
              </Form.Reset>
            </Form.Item>
          </Form>
          {skuList.length ? (
            <div>
              <div className={styles.container}>
                {skuList?.map((sku: GetActivitySkuResp) => {
                  return (
                    <div className={styles.skuContainer} key={sku.skuId}>
                      <img className={styles.skuImg} src={sku.imagePath} alt="" />
                      <div>
                        <div className={styles.skuName}>{sku.name}</div>
                        <div className={styles.skuId}>SKUID:{sku.skuId}</div>
                        <div className={styles.price}>¥ {sku.price}</div>
                        <Button type="primary" size="small" onClick={() => syncImage(sku)}>
                          同步图片
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ) : (
            <LzEmpty />
          )}
        </Loading>
      </Drawer>
      <LzDialog
        title="图片同步结果"
        visible={synced}
        footer={false}
        onClose={() => {
          setSynced(false);
          getSkuImgList(targetRow);
        }}
      >
        <div className={styles.syncedContainer}>
          <div className={styles.syncedItem}>
            <img src={targetSku.imagePath} alt="" />
            <div>同步前</div>
          </div>
          <Icon type="arrow-double-right" style={{ margin: '0 15px' }} />
          <div className={styles.syncedItem}>
            <img src={syncedSku.imagePath} alt="" />
            <div>同步后</div>
          </div>
        </div>
      </LzDialog>
    </div>
  );
};
