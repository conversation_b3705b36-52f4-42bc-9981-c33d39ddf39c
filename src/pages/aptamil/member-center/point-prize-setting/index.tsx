import React, { useEffect, useRef, useState } from 'react';
import {
  Field,
  Form,
  Input,
  Radio,
  Button,
  Table,
  NumberPicker,
  Box,
  Card,
  Tab,
  DatePicker2,
  Loading,
  Dialog,
} from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzMsg from '@/components/LzMsg';
import dayjs from 'dayjs';
import ChoosePrize from './ChoosePrize';
import LzDialog from '@/components/LzDialog';
import {
  memberPointExchangeGetPointExchange,
  memberPointExchangeGetPointExchangeRule,
  memberPointExchangeAddPointExchange,
  memberPointExchangeDeletePrize,
} from '@/api/aptamil';
import type {
  AptamilMemberPointExchangeConditionAddRequest,
  AptamilMemberPointExchangeAddPrizeRequest,
} from '@/api/types';

interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  hasSendTotalCount: number;
  prizeKey: string;
  planStatus: number;
}
enum prizeTypeName {
  '实物' = 3,
  '礼品卡' = 7,
}
export default () => {
  const field = Field.useField();
  const FormItem = Form.Item;
  const [loading, setLoading] = useState(false);

  const [level, setLevel] = useState(2);

  const defaultCondition: AptamilMemberPointExchangeConditionAddRequest = {
    activityId: '',
    id: '',
    orderEndTime: '',
    orderPrice: 0,
    orderPriceLimit: 2,
    orderStartTime: '',
    prizeLevel: 0,
    prizeLevelLimit: 1,
    prizeList: [],
    rule: '',
    shopId: '',
  };
  const tableColumns = [
    {
      title: '积分数量',
      align: 'center',
      dataIndex: 'exchangePointNum',
      cell: (value, index, record) => {
        return (
          <NumberPicker
            min={0}
            max={999999}
            step={1}
            value={record.exchangePointNum}
            onChange={(value: number) => {
              const newPrizeList = [...prizeList];
              newPrizeList[index].exchangePointNum = value;
              setPrizeList(newPrizeList);
            }}
            type="inline"
            defaultValue={0}
          />
        );
      },
    },
    {
      title: '兑换时间',
      align: 'center',

      cell: (value, index, record) => {
        return (
          <DatePicker2.RangePicker
            showTime
            value={[record.exchangeStartTime, record.exchangeEndTime]}
            onChange={(value: any) => {
              const newPrizeList = [...prizeList];
              newPrizeList[index].exchangeStartTime = dayjs(value[0]).format('YYYY-MM-DD HH:mm:ss');
              newPrizeList[index].exchangeEndTime = dayjs(value[1]).format('YYYY-MM-DD HH:mm:ss');
              setPrizeList(newPrizeList);
            }}
            hasClear={false}
            style={{ width: '300px' }}
          />
        );
      },
    },
    {
      title: '奖品名称',
      align: 'center',
      dataIndex: 'prizeName',
    },
    {
      title: '奖品类型',
      align: 'center',
      dataIndex: 'prizeType',
      cell: (value, index, record) => {
        return prizeTypeName[value];
      },
    },
    {
      title: '单位数量',
      align: 'center',
      dataIndex: 'unitCount',
    },
    {
      title: '单份价值（元）',
      align: 'center',
      dataIndex: 'unitPrice',
    },
    {
      title: '发放份数',
      align: 'center',
      dataIndex: 'sendTotalCount',
    },
    {
      title: '操作',
      align: 'center',
      cell: (value, index, record: AptamilMemberPointExchangeAddPrizeRequest) => {
        return (
          <Box direction="row" justify="flex-start" align="center" spacing={10}>
            <Button
              type="primary"
              onClick={() => {
                console.log('🚀 ~ record:', record);
                isEdit.current = true;

                setEditValue({
                  dayLimitType: record.dayLimitType as number,
                  prizeImg: record.prizeImg || '',
                  prizeName: record.prizeName || '',
                  prizeType: record.prizeType as number,
                  sendTotalCount: record.sendTotalCount || 0,
                  unitCount: record.unitCount || 0,
                  unitPrice: record.unitPrice || 0,
                  dayLimit: record.dayLimit || 0,
                  probability: '',
                  ifPlan: 0,
                  prizeKey: record.prizeKey || '',
                  planStatus: 2,
                  hasSendTotalCount: record.hasSendTotalCount || 0,
                });
                setVisible(true);
              }}
            >
              编辑
            </Button>
            <Button
              warning
              onClick={async () => {
                try {
                  console.log('🚀 ~ record:', record);
                  // 查询 conditionAddRequests.current和prizeList中除了record以外 是否存在开始时间在当前时间之前的奖品  如果没有则报错返回
                  const now = dayjs();
                  const hasConditionAdd = conditionAddRequests.current.some(
                    (item: AptamilMemberPointExchangeConditionAddRequest) => {
                      return item?.prizeList!.some((it) => {
                        return it.prizeLevel !== record.prizeLevel && dayjs(it.exchangeStartTime).isBefore(now);
                      });
                    },
                  );
                  console.log('🚀 ~ onClick={ ~ hasConditionAdd:', hasConditionAdd);
                  const hasPrizeList = prizeList.some(
                    (item) => item.prizeKey !== record.prizeKey && dayjs(item.exchangeStartTime).isBefore(now),
                  );
                  console.log('🚀 ~ onClick={ ~ hasPrizeList:', hasPrizeList);
                  const hasStartTimeBeforeNow = hasConditionAdd || hasPrizeList;
                  console.log('🚀 ~ hasStartTimeBeforeNow ~ hasStartTimeBeforeNow:', hasStartTimeBeforeNow);
                  if (!hasStartTimeBeforeNow) {
                    LzMsg.error('请至少配置一个可以兑换的奖品~');
                    return;
                  }
                  Dialog.confirm({
                    content: '确认删除该奖品吗？',
                    onOk: async () => {
                      if (record.prizeId) {
                        setLoading(true);
                        const res = await memberPointExchangeDeletePrize({
                          prizeId: record.prizeId,
                        });
                        console.log('🚀 ~ onOk: ~ res:', res);
                        setLoading(false);
                        LzMsg.success('删除成功');
                        getPointExchange();
                      } else {
                        const newPrizeList = [...prizeList];
                        newPrizeList.splice(index, 1);
                        setPrizeList(newPrizeList);
                        LzMsg.success('删除成功');
                      }
                    },
                  });
                } catch (error) {
                  console.log('🚀 ~ onClick={ ~ error:', error);

                  LzMsg.error(error.message);
                  setLoading(false);
                }
              }}
            >
              删除
            </Button>
            {index >= 1 && (
              <Button
                type="primary"
                onClick={() => {
                  console.log('🚀 ~ record:', record);
                  //  当前sortid设置成第一条数据的sortid减1 并按sortId排序
                  const newPrizeList = [...prizeList];
                  newPrizeList[index].sortId = newPrizeList[0]?.sortId! - 1;
                  newPrizeList.sort((a, b) => a.sortId! - b.sortId!);
                  setPrizeList(newPrizeList);
                  conditionAddRequests.current[level - 2].prizeList = newPrizeList;
                }}
              >
                置顶
              </Button>
            )}
          </Box>
        );
      },
    },
  ];
  const conditionAddRequests = useRef<AptamilMemberPointExchangeConditionAddRequest[]>([
    { ...defaultCondition, prizeLevel: 2 },
    { ...defaultCondition, prizeLevel: 3 },
    { ...defaultCondition, prizeLevel: 4 },
    { ...defaultCondition, prizeLevel: 5 },
  ]);
  const [prizeList, setPrizeList] = useState<AptamilMemberPointExchangeAddPrizeRequest[]>([]);
  const complementData = (arr1, arr2) => {
    return arr1.map((item1) => {
      const item2 = arr2.find((item) => item.prizeLevel === item1.prizeLevel) || {
        ...defaultCondition,
        prizeLevel: item1.prizeLevel,
        orderTime: [],
      };
      return {
        ...item2,
        orderTime: item2.orderStartTime ? [item2.orderStartTime, item2.orderEndTime] : [],
      };
    });
  };

  const getActRule = async () => {
    const result = await memberPointExchangeGetPointExchangeRule();
    field.setValue('actRule', result);
  };
  useEffect(() => {
    getPointExchange();
    getActRule();
  }, []);
  //   判断信息是否完整 然后保存数据
  const isCompleteData = () => {
    // 判断当前信息是否填写完整
    field.validate((errors) => {
      if (!errors) {
        // 判断奖品信息是否填写完整
        const emptyPrize = prizeList.some((item) => {
          return item.exchangePointNum === 0 || item.exchangeStartTime === '' || item.exchangeEndTime === '';
        });
        if (emptyPrize) {
          throw new Error('当前等级中奖品积分或兑换时间未填写完整~');
        }
        conditionAddRequests.current[level - 2] = {
          ...conditionAddRequests.current[level - 2],
          ...field.getValues(),
          prizeList: prizeList,
        };
      } else {
        throw new Error('请完善信息~');
      }
    });
    return true;
  };
  //   切换等级
  const changheLevel = (level: number) => {
    try {
      if (prizeList.length > 0 && !isCompleteData()) {
        return;
      }
      const info = conditionAddRequests.current.find((item) => item.prizeLevel === level) || {};
      field.setValues({ ...info });
      setPrizeList(info.prizeList || []);
      setLevel(level);
    } catch (error) {
      LzMsg.error(error.message);
    }
  };
  // 添加积分规则
  const handleAddReward = async () => {
    try {
      if (!isCompleteData()) return;
      const data = conditionAddRequests.current.filter(
        (item: AptamilMemberPointExchangeConditionAddRequest) => item.prizeList?.length,
      );
      //   判断data为空时 报错返回
      if (data.length === 0) {
        LzMsg.error('请至少配置一个等级奖品~');
        return;
      }
      //   查询data中是否有开始时间在当前时间之前的  没有则报错返回
      const hasStartTimeBeforeNow = data.some((item) => {
        const startTime = dayjs(item.orderStartTime);
        return startTime.isBefore(dayjs());
      });
      console.log('🚀 ~ hasStartTimeBeforeNow ~ hasStartTimeBeforeNow:', hasStartTimeBeforeNow);
      if (!hasStartTimeBeforeNow) {
        LzMsg.error('请至少配置一个可以兑换的奖品~');
        return;
      }
      setLoading(true);
      const result = await memberPointExchangeAddPointExchange({
        actRule: field.getValues().actRule as string,
        conditionAddRequests: data,
      });
      console.log('🚀 ~ handleAddReward ~ result:', result);
      setLoading(false);
      conditionAddRequests.current = complementData(conditionAddRequests.current, result);
      setPrizeList(result[level - 2].prizeList || []);
      LzMsg.success('保存成功');
      getActRule();
    } catch (error) {
      setLoading(false);
      LzMsg.error(error.message);
    }
  };
  const getPointExchange = async () => {
    setLoading(true);
    const result = await memberPointExchangeGetPointExchange();
    setLoading(false);
    conditionAddRequests.current = complementData(conditionAddRequests.current, result);
    field.setValues({ ...conditionAddRequests.current[0] });
    setPrizeList(result[level - 2].prizeList || []);
  };

  const [visible, setVisible] = useState(false);
  const isEdit = useRef(false);
  const [editValue, setEditValue] = useState<PrizeInfo>({
    prizeName: '',
    prizeImg: '',
    prizeType: 0,
    probability: '',
    unitCount: 0,
    unitPrice: 0,
    sendTotalCount: 0,
    dayLimit: 0,
    dayLimitType: 0,
    ifPlan: 0,
    hasSendTotalCount: 0,
    prizeKey: '',
    planStatus: 2,
  });

  const onCancel = () => {
    setVisible(false);
    isEdit.current = false;
  };
  //   修改下单时间
  const changeOrderTime = (value: any) => {
    field.setValue('orderStartTime', dayjs(value[0]).format('YYYY-MM-DD HH:mm:ss'));
    field.setValue('orderEndTime', dayjs(value[1]).format('YYYY-MM-DD HH:mm:ss'));
  };
  //   修改奖品信息
  const handleEditPrize = (choosePrize: PrizeInfo) => {
    console.log('🚀 ~ handleEditPrize ~ choosePrize:', choosePrize);
    console.log(isEdit.current, 'isEdit.current');
    if (isEdit.current) {
      // 编辑奖品 直接替换原来奖品
      const newPrizeList = [...prizeList];
      const index = newPrizeList.findIndex((item) => item.prizeKey === choosePrize.prizeKey);
      newPrizeList[index] = {
        ...newPrizeList[index],
        ...choosePrize,
      };
      setPrizeList(newPrizeList);
      setVisible(false);
      return;
    }
    // 新增 需要判断不能重复prizeKey
    const hasPrizeKey = prizeList.some((item) => item.prizeKey === choosePrize.prizeKey);
    if (hasPrizeKey) {
      LzMsg.error('当前奖品已存在~');
      return;
    }
    // 查询其他等级奖品 是否存在相同的prizeKey
    const hasSamePrizeKey = conditionAddRequests.current.some(
      (item) => item.prizeList?.some((prize) => prize.prizeKey === choosePrize.prizeKey) && item.prizeLevel !== level,
    );
    if (hasSamePrizeKey) {
      LzMsg.error('当前奖品已存在其他等级~');
      return;
    }
    setPrizeList([
      ...prizeList,
      {
        ...choosePrize,
        activityId: '',
        exchangeEndTime: '',
        exchangePointNum: 0,
        exchangeStartTime: '',
        itemId: '',
        levelConditionId: '',
        prizeId: '',
        prizeLevel: level,
        sortId: prizeList.length + 1,
      },
    ]);
    setVisible(false);
  };

  return (
    <Loading visible={loading} fullScreen>
      <LzPanel>
        <Box direction="row" justify="space-between" align="center" style={{ marginBottom: 20 }}>
          <h3>下单积分奖励配置</h3>
          <Button type="primary" size="large" onClick={handleAddReward}>
            保存当前设置
          </Button>
        </Box>
        <hr></hr>
        <Form field={field} labelAlign="top" useLabelForErrorMessage>
          <Card title="活动规则">
            <FormItem label="活动规则" required name="actRule">
              <Input.TextArea placeholder="请输入活动规则" />
            </FormItem>
          </Card>
          <LzPanel>
            <Tab activeKey={`${level - 2}`}>
              <Tab.Item title="LV2 设置" onClick={() => changheLevel(2)}></Tab.Item>
              <Tab.Item title="LV3 设置" onClick={() => changheLevel(3)}></Tab.Item>
              <Tab.Item title="LV4 设置" onClick={() => changheLevel(4)}></Tab.Item>
              <Tab.Item title="LV5 设置" onClick={() => changheLevel(5)}></Tab.Item>
            </Tab>
            <Card title="下单条件配置" contentHeight={0}></Card>
            <LzPanel>
              <FormItem label="下单时间配置" required name="orderTime">
                <DatePicker2.RangePicker
                  onChange={changeOrderTime}
                  showTime
                  hasClear={false}
                  style={{ width: '300px' }}
                />
              </FormItem>
              <FormItem label="下单金额配置(京东价)" required name="orderPriceLimit">
                <Radio.Group onChange={(value) => field.setValue('orderPriceLimit', value)}>
                  <Radio value={2}>不限额</Radio>
                  <Radio value={1}>限额</Radio>
                </Radio.Group>
              </FormItem>
              {field.getValues().orderPriceLimit === 1 && (
                <FormItem label="总订单金额" required name="orderPrice">
                  大于等于
                  <NumberPicker
                    type="inline"
                    value={field.getValues().orderPrice as number}
                    onChange={(value) => field.setValue('orderPrice', value)}
                    step={0.01}
                    min={0.0}
                    max={9999999}
                  />
                  元
                </FormItem>
              )}

              <FormItem label="奖品等级领取配置" required name="prizeLevelLimit">
                <Radio.Group>
                  <Radio value={1}>仅当前等级可领</Radio>
                  <Radio value={2}>大于等于当前等级可领</Radio>
                </Radio.Group>
              </FormItem>
              <FormItem label="活动提示" required name="rule">
                <Input.TextArea maxLength={39} placeholder="请输入活动提示" />
              </FormItem>
            </LzPanel>
            <Card title="积分奖品设置" contentHeight={0}></Card>
            <LzPanel>
              <Button
                type="primary"
                disabled={prizeList.length >= 10}
                size="medium"
                onClick={() => {
                  isEdit.current = false;
                  setEditValue(null);
                  setVisible(true);
                }}
              >
                添加奖品{prizeList.length}/10
              </Button>
              <Table style={{ marginTop: 20 }} columns={tableColumns} dataSource={prizeList} hasBorder={false}></Table>
            </LzPanel>
          </LzPanel>
        </Form>

        <LzDialog
          title={false}
          visible={visible}
          footer={false}
          onClose={() => setVisible(false)}
          style={{ width: '670px' }}
        >
          <ChoosePrize
            initTarget={3}
            typeList={[3, 7]}
            editValue={editValue}
            hasProbability={false}
            hasLimit={true}
            onChange={(choosePrize: any) => {
              handleEditPrize(choosePrize);
            }}
            onCancel={onCancel}
          />
        </LzDialog>
      </LzPanel>
    </Loading>
  );
};
