/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer } from 'react';
import { Form, Input, Table } from '@alifd/next';
import { formItemLayout, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';
import ChoosePromotion from '@/components/ChoosePromotion';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">店铺会员</FormItem>
        <FormItem label="参与规则">
          1.会员入会后未有过任何一笔订单状态为完成的正装商品订单 2.会员首购有礼同一用户只享受一次
        </FormItem>

        <FormItem label="校验历史订单天数">{formData.beforeDay}天</FormItem>
        <FormItem label="生日月前订单笔数">{formData.beforeOrderNum}笔</FormItem>
        <FormItem label="生日月内订单笔数">{formData.afterOrderNum}笔</FormItem>
        <FormItem label="生日月内单笔订单金额">{formData.afterOrderPrice}元</FormItem>

        <FormItem label="邀请有礼奖品设置" isPreview={false}>
          <Table
            dataSource={formData.prizeList.filter((e) => e.prizeName !== '谢谢参与' || !e.prizeName)}
            style={{ marginTop: '15px' }}
          >
            <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
            <Table.Column title="邀请入会人数" cell={(_, index, row) => <div>{row.peopleNum}</div>} />
            <Table.Column title="奖项名称" dataIndex="prizeName" />
            <Table.Column
              title="奖项类型"
              cell={(_, index, row) => (
                <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                  {PRIZE_TYPE[row.prizeType]}
                </div>
              )}
            />
            <Table.Column
              title="单位数量"
              cell={(_, index, row) => {
                if (row.prizeType === 1) {
                  return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                } else {
                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                }
              }}
            />
            <Table.Column
              title="发放份数"
              cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
            />
            <Table.Column
              title="奖品图"
              cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
            />
          </Table>
        </FormItem>
      </Form>
    </div>
  );
};
