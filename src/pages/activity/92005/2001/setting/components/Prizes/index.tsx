/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useEffect, useImperativeHandle, useState } from 'react';
import { Form, Field, Radio, Table, Button, Dialog, Message } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { FormLayout, PageData, PRIZE_INFO } from '../../../util';
// import LzPrize from './LzPrize';
import { activityEditDisabled, deepCopy, isDisableSetPrize } from '@/utils';
import { PRIZE_TYPE } from '../../../util';
import ChoosePrize from '@/components/ChoosePrizeForDZ';
import LzDialog from '@/components/LzDialog';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();
  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  const [plan, setPlan] = useState<any[]>([]);
  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  useEffect((): void => {
    if (!formData.prizeList.length) {
      const prizeList: any = [];
      for (let i = 0; i < 1; i++) {
        prizeList.push(deepCopy(PRIZE_INFO));
      }
      setFormData({ prizeList });
    }
  }, []);

  useImperativeHandle(sRef, (): { submit: () => object | null } => ({
    submit: () => {
      console.log('prize=========');
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));

  const onPrizeChange = (data): boolean | void => {
    if (activityEditDisabled() && data.prizeName !== '谢谢参与') {
      if (data.sendTotalCount < defaultValue.prizeList[target].sendTotalCount) {
        Message.error(`发放份数不能小于${defaultValue.prizeList[target].sendTotalCount}份`);
        return false;
      }
    }
    // 更新指定index 奖品信息
    formData.prizeList[target] = {
      ...data,
      type: 0,
      prizeKey: data.planId || data.promoId || data.prizeKey,
      prizeName: data.prizeName || data.planName,
      peopleNum: formData.prizeList[target].peopleNum,
      sortId: target,
    };
    const list = formData.prizeList.map((item) => item.prizeKey);
    if (data.prizeType === 11) {
      formData.giftSkuList = data.skuList;
    }
    setPlan(list);
    setData(formData);
    setVisible(false);
  };
  const onCancel = (): void => {
    setVisible(false);
  };
  return (
    <div>
      <LzPanel title="生日礼奖品设置">
        <Form {...formItemLayout} field={field}>
          <FormItem label="奖品设置" required>
            <Table dataSource={formData.prizeList}>
              <Table.Column
                title="奖品名称"
                dataIndex="prizeName"
                cell={(_, index, row) => <div>{row.prizeName}</div>}
              />
              <Table.Column
                title="奖品类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              {/* <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => (
                  <div>
                    {PRIZE_TYPE[row.prizeType] && row.unitPrice === 0
                      ? row.unitPrice
                      : row.unitPrice
                      ? Number(row.unitPrice).toFixed(2)
                      : ''}
                  </div>
                )}
              /> */}
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="操作"
                width={130}
                cell={(val, index, _) => (
                  <FormItem style={{ marginBottom: '0' }} disabled={isDisableSetPrize(formData.prizeList, index)}>
                    <Button
                      text
                      type="primary"
                      onClick={() => {
                        let row = formData.prizeList[index];
                        if (!row.prizeName) {
                          row = null;
                        }
                        setEditValue(row || null);
                        setTarget(index);
                        setVisible(true);
                      }}
                    >
                      <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                    </Button>
                  </FormItem>
                )}
              />
            </Table>
          </FormItem>

          <LzDialog
            title={false}
            visible={visible}
            footer={false}
            onClose={() => setVisible(false)}
            style={{ width: '670px' }}
          >
            <ChoosePrize
              formData={formData}
              editValue={editValue}
              hasLimit={false}
              hasProbability={false}
              typeList={[11]}
              defaultTarget={11}
              onChange={onPrizeChange}
              onCancel={onCancel}
              planList={plan}
              promoType={['1']}
              defaultEditValue={defaultValue.prizeList[target] as any}
              width={500}
              height={500}
              prizeNameLength={25}
            />
          </LzDialog>
        </Form>
      </LzPanel>
    </div>
  );
};
