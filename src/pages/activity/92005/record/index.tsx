/**
 * 大转盘抽奖数据报表
 */
import React, { useEffect, useState } from 'react';
import { Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import PromotionRecord from './components/PromotionRecord';
import LzDocGuide from '@/components/LzDocGuide';
// import { num } from '@/api/v21003';
// import { getParams } from '@/utils';
// import exportCombinedLogs from '@/utils/exportAll';

export default () => {
  // const [actNum, setActNum] = useState(12);

  // const getNum = async () => {
  //   try {
  //     const data = await num({ activityId: getParams('id') });
  //     setActNum(Number(data));
  //   } catch (e) {
  //     console.error(e);
  //   }
  // };

  useEffect(() => {
    // getNum();
  }, []);

  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
      <LzPanel title="会生日礼数据报表" actions={<LzDocGuide />}>
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="令牌操作记录" key="1">
            {/* actNum={actNum} */}
            <PromotionRecord />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
