import {
  Activity99101CreateOrUpdateRequest,
  Activity99101CreateOrUpdateResponse,
  Activity99101DataReportRequest,
  Activity99101DataReportResponse,
  Activity99101TemplateResponse,
  IPageActivity99101DataDetailResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 伊利定制-转段有礼
 * @summary 创建活动
 * @request POST:/99101/createActivity
 */
export const createActivity = (
  request: Activity99101CreateOrUpdateRequest,
): Promise<Activity99101CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/99101/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利转段数据报表
 * @summary 伊利转段活动明细数据导出
 * @request POST:/99101/data/detail/export
 */
export const dataDetailExport = (request: Activity99101DataReportRequest): Promise<void> => {
  return httpRequest({
    url: '/99101/data/detail/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利转段数据报表
 * @summary 伊利转段活动明细数据查询
 * @request POST:/99101/data/detail/query
 */
export const dataDetailQuery = (
  request: Activity99101DataReportRequest,
): Promise<IPageActivity99101DataDetailResponse> => {
  return httpRequest({
    url: '/99101/data/detail/query',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利转段数据报表
 * @summary 伊利转段活动数据导出
 * @request POST:/99101/data/report/export
 */
export const dataReportExport = (request: Activity99101DataReportRequest): Promise<void> => {
  return httpRequest({
    url: '/99101/data/report/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利转段数据报表
 * @summary 伊利转段活动数据查询
 * @request POST:/99101/data/report/query
 */
export const dataReportQuery = (request: Activity99101DataReportRequest): Promise<Activity99101DataReportResponse> => {
  return httpRequest({
    url: '/99101/data/report/query',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利定制-转段有礼
 * @summary 下载模板
 * @request POST:/99101/template/export
 */
export const templateExport = (): Promise<void> => {
  return httpRequest({
    url: '/99101/template/export',
    method: 'post',
  });
};

/**
 * @tags 伊利定制-转段有礼
 * @summary 导入模板
 * @request POST:/99101/template/import
 */
export const templateImport = (file: any): Promise<Activity99101TemplateResponse[]> => {
  return httpRequest({
    url: '/99101/template/import',
    method: 'post',
    data: file,
  });
};

/**
 * @tags 伊利定制-转段有礼
 * @summary 修改活动
 * @request POST:/99101/updateActivity
 */
export const updateActivity = (
  request: Activity99101CreateOrUpdateRequest,
): Promise<Activity99101CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/99101/updateActivity',
    method: 'post',
    data: request,
  });
};
